import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/services/advanced_ml_service.dart';
import '../../core/services/realtime_prediction_service.dart';
import '../../models/ml_models.dart';
import '../../core/theme/app_theme.dart';
import 'dart:async';

/// Dashboard avanzado con predicciones en tiempo real - FASE 2
class AdvancedDashboardScreen extends StatefulWidget {
  const AdvancedDashboardScreen({super.key});

  @override
  State<AdvancedDashboardScreen> createState() => _AdvancedDashboardScreenState();
}

class _AdvancedDashboardScreenState extends State<AdvancedDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  Timer? _refreshTimer;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshTimer?.cancel();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    
    final mlService = context.read<AdvancedMLService>();
    final realtimeService = context.read<RealtimePredictionService>();
    
    if (!mlService.isInitialized) {
      await mlService.initialize();
    }
    
    if (!realtimeService.isActive) {
      await realtimeService.initialize();
    }
    
    setState(() => _isLoading = false);
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (mounted) {
        setState(() {}); // Trigger rebuild para actualizar datos
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.auto_awesome, color: Colors.white),
            SizedBox(width: 8),
            Text('🚀 IA Avanzada'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.timeline), text: 'Tiempo Real'),
            Tab(icon: Icon(Icons.analytics), text: 'Métricas ML'),
            Tab(icon: Icon(Icons.warning), text: 'Alertas'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Actualizar datos',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('🤖 Inicializando IA avanzada...'),
                ],
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildRealtimeTab(),
                _buildMetricsTab(),
                _buildAlertsTab(),
              ],
            ),
    );
  }

  Widget _buildDashboardTab() {
    return Consumer2<AdvancedMLService, RealtimePredictionService>(
      builder: (context, mlService, realtimeService, child) {
        return RefreshIndicator(
          onRefresh: _refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildServiceStatusCard(mlService, realtimeService),
                const SizedBox(height: 16),
                _buildPredictionSummaryCard(realtimeService),
                const SizedBox(height: 16),
                _buildHighRiskPredictionsCard(realtimeService),
                const SizedBox(height: 16),
                _buildModelPerformanceCard(mlService),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRealtimeTab() {
    return Consumer<RealtimePredictionService>(
      builder: (context, service, child) {
        final activePredictions = service.activePredictions.values.toList();
        
        if (activePredictions.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.hourglass_empty, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No hay predicciones activas'),
                SizedBox(height: 8),
                Text('Las predicciones aparecerán aquí en tiempo real'),
              ],
            ),
          );
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: activePredictions.length,
          itemBuilder: (context, index) {
            final prediction = activePredictions[index];
            return _buildRealtimePredictionCard(prediction);
          },
        );
      },
    );
  }

  Widget _buildMetricsTab() {
    return Consumer<AdvancedMLService>(
      builder: (context, service, child) {
        final metrics = service.modelMetrics;
        final stats = service.getServiceStats();
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMetricsOverviewCard(metrics),
              const SizedBox(height: 16),
              _buildModelStatsCard(stats),
              const SizedBox(height: 16),
              _buildTrainingInfoCard(service),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAlertsTab() {
    return Consumer<RealtimePredictionService>(
      builder: (context, service, child) {
        final alerts = service.getActiveAlerts();
        final highRiskPredictions = service.getHighRiskPredictions();
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildActiveAlertsCard(alerts),
              const SizedBox(height: 16),
              _buildHighRiskCard(highRiskPredictions),
            ],
          ),
        );
      },
    );
  }

  Widget _buildServiceStatusCard(AdvancedMLService mlService, RealtimePredictionService realtimeService) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('Estado de Servicios', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'ML Avanzado',
                    mlService.isInitialized,
                    mlService.isTraining ? 'Entrenando...' : 'Listo',
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    'Tiempo Real',
                    realtimeService.isActive,
                    realtimeService.isActive ? 'Activo' : 'Inactivo',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (mlService.lastTraining != null)
              Text(
                '🕒 Último entrenamiento: ${_formatDateTime(mlService.lastTraining!)}',
                style: const TextStyle(color: Colors.grey, fontSize: 12),
              ),
            if (realtimeService.lastUpdate != null)
              Text(
                '🔄 Última actualización: ${_formatDateTime(realtimeService.lastUpdate!)}',
                style: const TextStyle(color: Colors.grey, fontSize: 12),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String title, bool isActive, String status) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? Colors.green.withValues(alpha: 0.3) : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            isActive ? Icons.check_circle : Icons.error,
            color: isActive ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            textAlign: TextAlign.center,
          ),
          Text(
            status,
            style: const TextStyle(fontSize: 10, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionSummaryCard(RealtimePredictionService service) {
    final stats = service.getRealtimeStats();
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('Resumen de Predicciones', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    '📊 Activas',
                    '${stats['activePredictions']}',
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    '🚨 Alto Riesgo',
                    '${stats['highRiskPredictions']}',
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    '🎯 Precisión',
                    '${(stats['averageAccuracy'] * 100).toInt()}%',
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHighRiskPredictionsCard(RealtimePredictionService service) {
    final highRiskPredictions = service.getHighRiskPredictions();
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.warning, color: Colors.red),
                SizedBox(width: 8),
                Text('Predicciones de Alto Riesgo', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            if (highRiskPredictions.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 48),
                      SizedBox(height: 8),
                      Text('✅ No hay predicciones de alto riesgo'),
                    ],
                  ),
                ),
              )
            else
              ...highRiskPredictions.take(3).map((prediction) => 
                _buildHighRiskItem(prediction)
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHighRiskItem(AdvancedPrediction prediction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.local_shipping, color: Colors.red.shade700),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'VH ${prediction.vhId}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${(prediction.probabilidadNovedad * 100).toInt()}% probabilidad de ${prediction.tipoNovedadMasProbable}',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${(prediction.probabilidadNovedad * 100).toInt()}%',
              style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModelPerformanceCard(AdvancedMLService service) {
    final metrics = service.modelMetrics;
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.speed, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('Rendimiento del Modelo', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem('Precisión', '${(metrics.accuracy * 100).toInt()}%', Colors.blue),
                ),
                Expanded(
                  child: _buildMetricItem('Recall', '${(metrics.recall * 100).toInt()}%', Colors.green),
                ),
                Expanded(
                  child: _buildMetricItem('F1-Score', '${(metrics.f1Score * 100).toInt()}%', Colors.orange),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '📊 Total predicciones: ${metrics.totalPredictions} | ✅ Correctas: ${metrics.correctPredictions}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildRealtimePredictionCard(AdvancedPrediction prediction) {
    final riskColor = _getRiskColor(prediction.probabilidadNovedad);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.local_shipping, color: riskColor),
                const SizedBox(width: 8),
                Text(
                  'VH ${prediction.vhId}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: riskColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${(prediction.probabilidadNovedad * 100).toInt()}%',
                    style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Tipo más probable: ${prediction.tipoNovedadMasProbable}',
              style: const TextStyle(color: Colors.grey),
            ),
            Text(
              'Confianza: ${(prediction.confianza * 100).toInt()}%',
              style: const TextStyle(color: Colors.grey),
            ),
            if (prediction.recomendaciones.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text('💡 Recomendaciones:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
              ...prediction.recomendaciones.take(2).map((rec) => 
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 2),
                  child: Text('• $rec', style: const TextStyle(fontSize: 11, color: Colors.grey)),
                ),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              '🕒 ${_formatDateTime(prediction.fechaPrediccion)}',
              style: const TextStyle(color: Colors.grey, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricsOverviewCard(ModelMetrics metrics) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.assessment, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('Métricas del Modelo', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetricsGrid(metrics),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricsGrid(ModelMetrics metrics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 2,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      children: [
        _buildMetricCard('Accuracy', metrics.accuracy, Colors.blue),
        _buildMetricCard('Precision', metrics.precision, Colors.green),
        _buildMetricCard('Recall', metrics.recall, Colors.orange),
        _buildMetricCard('F1-Score', metrics.f1Score, Colors.purple),
      ],
    );
  }

  Widget _buildMetricCard(String title, double value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${(value * 100).toInt()}%',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildModelStatsCard(Map<String, dynamic> stats) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('Estadísticas del Servicio', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatItem('Estado', stats['isInitialized'] ? 'Inicializado' : 'No inicializado'),
            _buildStatItem('Entrenando', stats['isTraining'] ? 'Sí' : 'No'),
            _buildStatItem('Cache Size', '${stats['cacheSize']}'),
            const SizedBox(height: 8),
            const Text('Modelos Cargados:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            ...((stats['modelsLoaded'] as Map<String, dynamic>).entries.map((entry) =>
              _buildStatItem('  ${entry.key}', entry.value ? 'Cargado' : 'No cargado')
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildTrainingInfoCard(AdvancedMLService service) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.school, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('Información de Entrenamiento', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            if (service.lastTraining != null)
              _buildStatItem('Último entrenamiento', _formatDateTime(service.lastTraining!))
            else
              const Text('No hay información de entrenamiento disponible'),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: service.isTraining ? null : () => _retrainModels(),
                icon: service.isTraining 
                    ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                    : const Icon(Icons.refresh),
                label: Text(service.isTraining ? 'Entrenando...' : 'Reentrenar Modelos'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveAlertsCard(List<String> alerts) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.notifications_active, color: Colors.red),
                SizedBox(width: 8),
                Text('Alertas Activas', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            if (alerts.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 48),
                      SizedBox(height: 8),
                      Text('✅ No hay alertas activas'),
                    ],
                  ),
                ),
              )
            else
              ...alerts.map((alert) => 
                Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange),
                      const SizedBox(width: 12),
                      Expanded(child: Text(alert)),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHighRiskCard(List<AdvancedPrediction> predictions) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.priority_high, color: Colors.red),
                SizedBox(width: 8),
                Text('VH de Alto Riesgo', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 16),
            if (predictions.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 48),
                      SizedBox(height: 8),
                      Text('✅ No hay VH de alto riesgo'),
                    ],
                  ),
                ),
              )
            else
              ...predictions.map((prediction) => _buildHighRiskItem(prediction)),
          ],
        ),
      ),
    );
  }

  Color _getRiskColor(double probability) {
    if (probability > 0.7) return Colors.red;
    if (probability > 0.4) return Colors.orange;
    return Colors.green;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _refreshData() async {
    setState(() => _isLoading = true);
    
    // Simular refresh
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() => _isLoading = false);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Datos actualizados'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _retrainModels() async {
    final mlService = context.read<AdvancedMLService>();
    
    try {
      await mlService.trainModels();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Modelos reentrenados exitosamente'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error reentrenando modelos: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
