// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'ReCount Pro';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get theme => 'Theme';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get systemTheme => 'System';

  @override
  String get language => 'Language';

  @override
  String get spanish => 'Spanish';

  @override
  String get english => 'English';

  @override
  String get conteo => 'Count';

  @override
  String get vhId => 'VH ID';

  @override
  String get placa => 'License Plate';

  @override
  String get productos => 'Products';

  @override
  String get sku => 'SKU';

  @override
  String get descripcion => 'Description';

  @override
  String get cantidad => 'Quantity';

  @override
  String get programado => 'Scheduled';

  @override
  String get fisico => 'Physical';

  @override
  String get diferencia => 'Difference';

  @override
  String get novedades => 'Issues';

  @override
  String get faltante => 'Missing';

  @override
  String get sobrante => 'Excess';

  @override
  String get guardar => 'Save';

  @override
  String get cancelar => 'Cancel';

  @override
  String get confirmar => 'Confirm';

  @override
  String get buscar => 'Search';

  @override
  String get filtrar => 'Filter';

  @override
  String get exportar => 'Export';

  @override
  String get imprimir => 'Print';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get loading => 'Loading...';

  @override
  String get retry => 'Retry';

  @override
  String get close => 'Close';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get general => 'General';

  @override
  String get metrics => 'Metrics';

  @override
  String get analytics => 'Analytics';

  @override
  String get sessionDuration => 'Session Duration';

  @override
  String get totalSessions => 'Total Sessions';

  @override
  String get productivity => 'Productivity';

  @override
  String get errorRate => 'Error Rate';

  @override
  String get version => 'Version';

  @override
  String get checkUpdates => 'Check for Updates';

  @override
  String get clearCache => 'Clear Cache';

  @override
  String get autoSync => 'Auto Sync';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get exportMetrics => 'Export Metrics';

  @override
  String get resetMetrics => 'Reset Metrics';

  @override
  String get collectMetrics => 'Collect Metrics';

  @override
  String get errorReporting => 'Error Reporting';
}
