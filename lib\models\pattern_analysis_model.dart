import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Modelo para almacenar datos de análisis de patrones
class PatternAnalysisData extends Equatable {
  final String id;
  final String vhId;
  final String placa;
  final String verificadorId;
  final String? conductorId;
  final String? ruta;
  final DateTime fechaConteo;
  final int diaSemana; // 1=Lunes, 7=Domingo
  final int hora; // 0-23
  final int cantidadSkus;
  final double pesoTotal;
  final bool tieneNovedad;
  final String? tipoNovedad; // 'Faltante', 'Sobrante'
  final List<String> skusConNovedad;
  final int tiempoConteoMinutos;
  final Map<String, dynamic> metadatos;

  const PatternAnalysisData({
    required this.id,
    required this.vhId,
    required this.placa,
    required this.verificadorId,
    this.conductorId,
    this.ruta,
    required this.fechaConteo,
    required this.diaSemana,
    required this.hora,
    required this.cantidadSkus,
    required this.pesoTotal,
    required this.tieneNovedad,
    this.tipoNovedad,
    required this.skusConNovedad,
    required this.tiempoConteoMinutos,
    required this.metadatos,
  });

  /// Crear desde datos de segundo conteo
  factory PatternAnalysisData.fromSegundoConteo(Map<String, dynamic> conteo) {
    final fecha = (conteo['fecha'] as Timestamp).toDate();
    final novedades = conteo['novedades'] as List? ?? [];
    
    return PatternAnalysisData(
      id: conteo['id'] ?? '',
      vhId: conteo['vhId'] ?? '',
      placa: conteo['placa'] ?? '',
      verificadorId: conteo['verificadorUid'] ?? '',
      conductorId: conteo['conductorId'],
      ruta: conteo['ruta'],
      fechaConteo: fecha,
      diaSemana: fecha.weekday,
      hora: fecha.hour,
      cantidadSkus: (conteo['productos'] as List?)?.length ?? 0,
      pesoTotal: _calcularPesoTotal(conteo['productos']),
      tieneNovedad: conteo['tieneNovedad'] ?? false,
      tipoNovedad: novedades.isNotEmpty ? novedades[0]['tipo'] : null,
      skusConNovedad: novedades.map<String>((n) => n['sku'] ?? '').toList(),
      tiempoConteoMinutos: conteo['tiempoConteoMinutos'] ?? 0,
      metadatos: {
        'vhProgramados': conteo['vhProgramados'] ?? 0,
        'vhSalen': conteo['vhSalen'] ?? 0,
        'totalNovedades': novedades.length,
      },
    );
  }

  /// Convertir a Map para Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'vhId': vhId,
      'placa': placa,
      'verificadorId': verificadorId,
      'conductorId': conductorId,
      'ruta': ruta,
      'fechaConteo': Timestamp.fromDate(fechaConteo),
      'diaSemana': diaSemana,
      'hora': hora,
      'cantidadSkus': cantidadSkus,
      'pesoTotal': pesoTotal,
      'tieneNovedad': tieneNovedad,
      'tipoNovedad': tipoNovedad,
      'skusConNovedad': skusConNovedad,
      'tiempoConteoMinutos': tiempoConteoMinutos,
      'metadatos': metadatos,
      'fechaCreacion': FieldValue.serverTimestamp(),
    };
  }

  /// Crear desde Map de Firebase
  factory PatternAnalysisData.fromMap(Map<String, dynamic> map) {
    return PatternAnalysisData(
      id: map['id'] ?? '',
      vhId: map['vhId'] ?? '',
      placa: map['placa'] ?? '',
      verificadorId: map['verificadorId'] ?? '',
      conductorId: map['conductorId'],
      ruta: map['ruta'],
      fechaConteo: (map['fechaConteo'] as Timestamp).toDate(),
      diaSemana: map['diaSemana'] ?? 1,
      hora: map['hora'] ?? 0,
      cantidadSkus: map['cantidadSkus'] ?? 0,
      pesoTotal: (map['pesoTotal'] ?? 0.0).toDouble(),
      tieneNovedad: map['tieneNovedad'] ?? false,
      tipoNovedad: map['tipoNovedad'],
      skusConNovedad: List<String>.from(map['skusConNovedad'] ?? []),
      tiempoConteoMinutos: map['tiempoConteoMinutos'] ?? 0,
      metadatos: Map<String, dynamic>.from(map['metadatos'] ?? {}),
    );
  }

  static double _calcularPesoTotal(dynamic productos) {
    if (productos == null) return 0.0;
    // Implementar cálculo real basado en productos
    return (productos as List).length * 10.0; // Peso promedio estimado
  }

  @override
  List<Object?> get props => [
        id,
        vhId,
        placa,
        verificadorId,
        conductorId,
        ruta,
        fechaConteo,
        diaSemana,
        hora,
        cantidadSkus,
        pesoTotal,
        tieneNovedad,
        tipoNovedad,
        skusConNovedad,
        tiempoConteoMinutos,
        metadatos,
      ];
}

/// Modelo para predicciones de patrones
class PatternPrediction extends Equatable {
  final double probabilidadNovedad;
  final String tipoNovedadMasProbable;
  final String? skuRiesgo;
  final double confianza;
  final List<String> recomendaciones;
  final Map<String, dynamic> factoresInfluencia;

  const PatternPrediction({
    required this.probabilidadNovedad,
    required this.tipoNovedadMasProbable,
    this.skuRiesgo,
    required this.confianza,
    required this.recomendaciones,
    required this.factoresInfluencia,
  });

  factory PatternPrediction.fromAnalysis(Map<String, dynamic> analysis) {
    return PatternPrediction(
      probabilidadNovedad: (analysis['probabilidadNovedad'] ?? 0.0).toDouble(),
      tipoNovedadMasProbable: analysis['tipoNovedadMasProbable'] ?? 'Faltante',
      skuRiesgo: analysis['skuRiesgo'],
      confianza: (analysis['confianza'] ?? 0.0).toDouble(),
      recomendaciones: List<String>.from(analysis['recomendaciones'] ?? []),
      factoresInfluencia: Map<String, dynamic>.from(analysis['factoresInfluencia'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [
        probabilidadNovedad,
        tipoNovedadMasProbable,
        skuRiesgo,
        confianza,
        recomendaciones,
        factoresInfluencia,
      ];
}

/// Modelo para patrones detectados
class DetectedPattern extends Equatable {
  final String id;
  final String tipo; // 'temporal', 'verificador', 'sku', 'vh'
  final String descripcion;
  final double frecuencia;
  final double impacto;
  final DateTime fechaDeteccion;
  final Map<String, dynamic> detalles;

  const DetectedPattern({
    required this.id,
    required this.tipo,
    required this.descripcion,
    required this.frecuencia,
    required this.impacto,
    required this.fechaDeteccion,
    required this.detalles,
  });

  factory DetectedPattern.fromMap(Map<String, dynamic> map) {
    return DetectedPattern(
      id: map['id'] ?? '',
      tipo: map['tipo'] ?? '',
      descripcion: map['descripcion'] ?? '',
      frecuencia: (map['frecuencia'] ?? 0.0).toDouble(),
      impacto: (map['impacto'] ?? 0.0).toDouble(),
      fechaDeteccion: (map['fechaDeteccion'] as Timestamp).toDate(),
      detalles: Map<String, dynamic>.from(map['detalles'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tipo': tipo,
      'descripcion': descripcion,
      'frecuencia': frecuencia,
      'impacto': impacto,
      'fechaDeteccion': Timestamp.fromDate(fechaDeteccion),
      'detalles': detalles,
    };
  }

  @override
  List<Object?> get props => [
        id,
        tipo,
        descripcion,
        frecuencia,
        impacto,
        fechaDeteccion,
        detalles,
      ];
}
