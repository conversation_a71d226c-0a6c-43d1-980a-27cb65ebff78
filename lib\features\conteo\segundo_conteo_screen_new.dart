import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/theme/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../models/vh_model.dart';
import '../../models/ml_models.dart';
import '../../core/services/validation_service.dart';
import '../../core/services/metrics_service.dart';
import '../../core/services/advanced_ml_service.dart';

class SegundoConteoScreenNew extends StatefulWidget {
  const SegundoConteoScreenNew({super.key});

  @override
  State<SegundoConteoScreenNew> createState() => _SegundoConteoScreenNewState();
}

class _SegundoConteoScreenNewState extends State<SegundoConteoScreenNew> {
  final _formKey = GlobalKey<FormState>();
  final _placaController = TextEditingController();
  final _vhProgramadosController = TextEditingController();
  final _vhSalenController = TextEditingController();

  // Variables para la sesión de segundo conteo
  final List<VhSegundoConteo> _vhContados = [];
  DateTime? _inicioSesion;

  // Variables para el VH actual
  bool _placaValidada = false;
  bool _tieneNovedad = false;
  String _tipoNovedad = 'Faltante';
  final _dtController = TextEditingController();
  final _skuController = TextEditingController();
  final _descripcionController = TextEditingController();
  final _alistadoController = TextEditingController();
  final _fisicoController = TextEditingController();
  final _verificadoController = TextEditingController();
  final _armadorController = TextEditingController();

  final List<NovedadConteo> _novedadesActuales = [];
  int _diferencia = 0;

  // ✅ NUEVO: Variables para búsqueda incremental de verificadores
  List<Map<String, String>> _verificadoresSugeridos = [];
  bool _mostrandoSugerenciasVerificador = false;
  Timer? _debounceTimerVerificador;

  // Variables para búsqueda incremental de placas
  List<String> _placasSugeridas = [];
  bool _mostrandoSugerenciasPlaca = false;
  Timer? _debounceTimerPlaca;

  @override
  void initState() {
    super.initState();
    _alistadoController.addListener(_calcularDiferenciaNovedad);
    _fisicoController.addListener(_calcularDiferenciaNovedad);

    // Inicializar tiempo de sesión para métricas
    _inicioSesion = DateTime.now();

    // Verificar datos de VH programados al inicializar
    _verificarDatosVhProgramados();

    // Diagnóstico de datos
    _diagnosticarDatos();
  }

  /// Diagnóstico de datos en Firebase
  void _diagnosticarDatos() async {
    try {
      print('🔍 [DIAGNÓSTICO] Iniciando diagnóstico de datos...');
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      await firebaseService.diagnosticarVhProgramados();
    } catch (e) {
      print('❌ [DIAGNÓSTICO] Error en diagnóstico: $e');
    }
  }

  /// Función temporal para verificar si hay datos de VH programados
  Future<void> _verificarDatosVhProgramados() async {
    try {
      print('🔍 [SEGUNDO_CONTEO] Verificando datos de VH programados...');
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);

      // Hacer una búsqueda con una letra común para ver si hay datos
      final placasTest = await firebaseService.buscarPlacas('A');
      print('📊 [SEGUNDO_CONTEO] Test con "A": ${placasTest.length} placas encontradas');

      if (placasTest.isEmpty) {
        // Intentar con otra letra
        final placasTest2 = await firebaseService.buscarPlacas('B');
        print('📊 [SEGUNDO_CONTEO] Test con "B": ${placasTest2.length} placas encontradas');

        if (placasTest2.isEmpty) {
          print('⚠️ [SEGUNDO_CONTEO] ADVERTENCIA: No se encontraron VH programados para hoy');
          print('   Esto puede significar que:');
          print('   1. No hay datos reales en la colección vh_programados');
          print('   2. No hay VH programados para la fecha actual');
          print('   3. Los datos están filtrados como datos de prueba');
          print('   4. Hay un problema con la consulta de Firebase');

          // Mostrar mensaje al usuario en lugar de crear datos de prueba
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('No se encontraron VH programados para hoy. Verifica que existan datos reales en Firebase.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 5),
              ),
            );
          }
        }
      }

      // Probar búsquedas específicas para verificar que funciona
      await _probarBusquedas();
    } catch (e) {
      print('❌ [SEGUNDO_CONTEO] Error verificando datos: $e');
    }
  }

  /// Crear VH reales de ejemplo (solo para testing inicial)
  Future<void> _crearDatosRealesEjemplo() async {
    try {
      print('🔧 [SEGUNDO_CONTEO] Creando datos reales de ejemplo...');

      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      // Usar exactamente la misma lógica de fecha que usa la búsqueda
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day, 12, 0, 0); // Mediodía de hoy

      // Placas reales proporcionadas por el usuario
      final vhReales = [
        {'vh_id': 'VH_REAL_001', 'placa': 'KSP873'},
        {'vh_id': 'VH_REAL_002', 'placa': 'LCN040'},
        {'vh_id': 'VH_REAL_003', 'placa': 'OAA994'},
      ];

      for (final vh in vhReales) {
        final docRef = firestore.collection('vh_programados').doc();
        batch.set(docRef, {
          'vh_id': vh['vh_id'],
          'placa': vh['placa'],
          'fecha': today,
          'tipo': 'real', // Marcar como dato real
          'productos': [],
          'created_at': FieldValue.serverTimestamp(),
          '_metadata': {
            'source': 'real_data',
            'created_by': 'user',
            'version': '1.0',
          },
        });
      }

      await batch.commit();
      print('✅ [SEGUNDO_CONTEO] ${vhReales.length} VH reales creados exitosamente');

      // Mostrar mensaje al usuario
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Se crearon ${vhReales.length} VH reales de ejemplo: ${vhReales.map((v) => v['placa']).join(', ')}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
      }

    } catch (e) {
      print('❌ [SEGUNDO_CONTEO] Error creando datos reales: $e');
    }
  }

  /// Probar búsquedas específicas para verificar funcionamiento
  Future<void> _probarBusquedas() async {
    try {
      print('🧪 [SEGUNDO_CONTEO] Probando búsquedas específicas...');
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);

      // Probar búsquedas con las placas que sabemos que existen
      final tests = ['A', 'AB', 'ABC', 'D', 'DE', 'DEF', 'G', 'GH', 'GHI'];

      for (final query in tests) {
        final resultados = await firebaseService.buscarPlacas(query);
        print('🔍 [TEST] "$query" -> ${resultados.length} resultados: ${resultados.join(', ')}');

        if (resultados.isNotEmpty) {
          print('✅ [TEST] Búsqueda funcionando correctamente con "$query"');
          break;
        }
      }
    } catch (e) {
      print('❌ [SEGUNDO_CONTEO] Error probando búsquedas: $e');
    }
  }

  @override
  void dispose() {
    _debounceTimerVerificador?.cancel();
    _debounceTimerPlaca?.cancel();
    _placaController.dispose();
    _vhProgramadosController.dispose();
    _vhSalenController.dispose();
    _dtController.dispose();
    _skuController.dispose();
    _descripcionController.dispose();
    _alistadoController.dispose();
    _fisicoController.dispose();
    _verificadoController.dispose();
    _armadorController.dispose();
    super.dispose();
  }



  /// Buscar placas de forma incremental
  Future<void> _buscarPlacas(String query) async {
    print('🔍 [SEGUNDO_CONTEO] Iniciando búsqueda de placas con query: "$query"');

    if (query.isEmpty) {
      print('📝 [SEGUNDO_CONTEO] Query vacío, ocultando sugerencias');
      setState(() {
        _mostrandoSugerenciasPlaca = false;
        _placasSugeridas = [];
      });
      return;
    }

    if (query.length < 2) {
      print('📝 [SEGUNDO_CONTEO] Query muy corto (${query.length} caracteres), esperando más texto');
      return;
    }

    try {
      print('🔄 [SEGUNDO_CONTEO] Llamando a FirebaseService.buscarPlacas...');
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      final placas = await firebaseService.buscarPlacas(query);

      print('📊 [SEGUNDO_CONTEO] Placas encontradas: ${placas.length}');
      for (int i = 0; i < placas.length; i++) {
        print('  ${i + 1}. ${placas[i]}');
      }

      if (mounted) {
        setState(() {
          _placasSugeridas = placas;
        });

        if (placas.isNotEmpty) {
          print('✅ [SEGUNDO_CONTEO] Mostrando sugerencias');
          setState(() {
            _mostrandoSugerenciasPlaca = true;
          });
        } else {
          print('❌ [SEGUNDO_CONTEO] No hay placas, ocultando sugerencias');
          setState(() {
            _mostrandoSugerenciasPlaca = false;
          });
        }
      }
    } catch (e, stackTrace) {
      print('❌ [SEGUNDO_CONTEO] Error buscando placas: $e');
      print('📍 [SEGUNDO_CONTEO] StackTrace: $stackTrace');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error buscando VH: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _seleccionarPlaca(String placa) {
    _placaController.text = placa;
    setState(() {
      _mostrandoSugerenciasPlaca = false;
      _placasSugeridas = [];
    });
    _validarPlaca();
  }

  void _validarPlaca() {
    // Validar entrada
    final validationResult = ValidationService.validatePlaca(_placaController.text);
    if (!validationResult.isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(validationResult.errorMessage!),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // Sanitizar la placa
    final placaSanitizada = ValidationService.sanitizePlaca(_placaController.text);
    
    // Verificar si ya fue contada
    final yaContada = _vhContados.any((vh) => vh.placa == placaSanitizada);
    if (yaContada) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('VH $placaSanitizada ya fue contado'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _placaValidada = true;
      _placaController.text = placaSanitizada;
    });

    // Mostrar confirmación
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('VH listo para conteo: $placaSanitizada'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _calcularDiferenciaNovedad() {
    final alistado = int.tryParse(_alistadoController.text) ?? 0;
    final fisico = int.tryParse(_fisicoController.text) ?? 0;
    setState(() {
      _diferencia = fisico - alistado;
    });
  }

  void _limpiarFormulario() {
    setState(() {
      _placaController.clear();
      _vhProgramadosController.clear();
      _vhSalenController.clear();
      _placaValidada = false;
      _tieneNovedad = false;
      _tipoNovedad = 'Faltante';
      _novedadesActuales.clear();
    });
    _limpiarFormularioNovedad();

    // Enfocar el campo de placa para el siguiente VH
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
      _placaController.clear();
    });
  }

  void _limpiarFormularioNovedad() {
    _dtController.clear();
    _skuController.clear();
    _descripcionController.clear();
    _alistadoController.clear();
    _fisicoController.clear();
    _verificadoController.clear();
    _armadorController.clear();
    setState(() {
      _diferencia = 0;
      _tipoNovedad = 'Faltante';
    });
  }

  // ✅ NUEVO: Métodos para búsqueda incremental de verificadores

  void _buscarVerificadoresIncremental(String query) async {
    if (query.isEmpty) {
      setState(() {
        _verificadoresSugeridos = [];
        _mostrandoSugerenciasVerificador = false;
      });
      return;
    }

    try {
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      final verificadores = await firebaseService.buscarVerificadoresIncremental(query);

      if (mounted) {
        setState(() {
          _verificadoresSugeridos = verificadores.take(5).toList();
          _mostrandoSugerenciasVerificador = verificadores.isNotEmpty;
        });
      }
    } catch (e) {
      print('Error buscando verificadores: $e');
      // Datos de ejemplo como fallback
      final verificadoresEjemplo = [
        {'nombre': 'Juan Pérez', 'email': '<EMAIL>'},
        {'nombre': 'María García', 'email': '<EMAIL>'},
        {'nombre': 'Carlos López', 'email': '<EMAIL>'},
        {'nombre': 'Ana Rodríguez', 'email': '<EMAIL>'},
        {'nombre': 'Luis Martínez', 'email': '<EMAIL>'},
      ].where((v) => v['nombre']!.toLowerCase().contains(query.toLowerCase())).toList();

      if (mounted) {
        setState(() {
          _verificadoresSugeridos = verificadoresEjemplo.take(5).toList();
          _mostrandoSugerenciasVerificador = verificadoresEjemplo.isNotEmpty;
        });
      }
    }
  }

  void _seleccionarVerificador(String nombre) {
    _verificadoController.text = nombre;
    setState(() {
      _mostrandoSugerenciasVerificador = false;
      _verificadoresSugeridos = [];
    });
  }

  void _onVerificadorChanged(String value) {
    _debounceTimerVerificador?.cancel();
    _debounceTimerVerificador = Timer(const Duration(milliseconds: 300), () {
      _buscarVerificadoresIncremental(value);
    });
  }

  void _onPlacaChanged(String value) {
    _debounceTimerPlaca?.cancel();
    _debounceTimerPlaca = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _buscarPlacas(value);
      }
    });
  }

  void _agregarNovedadCompleta() {
    // Validar campos obligatorios
    if (_dtController.text.isEmpty ||
        _skuController.text.isEmpty ||
        _descripcionController.text.isEmpty ||
        _alistadoController.text.isEmpty ||
        _fisicoController.text.isEmpty ||
        _armadorController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Por favor completa todos los campos'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final novedad = NovedadConteo(
      tipo: _tipoNovedad,
      dt: _dtController.text,
      sku: _skuController.text,
      descripcion: _descripcionController.text,
      alistado: int.parse(_alistadoController.text),
      fisico: int.parse(_fisicoController.text),
      diferencia: _diferencia,
      verificado: _verificadoController.text.trim(), // ✅ CORREGIDO: Usar texto del verificador
      armador: _armadorController.text,
    );

    setState(() {
      _novedadesActuales.add(novedad);
    });

    _limpiarFormularioNovedad();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Novedad ${_tipoNovedad.toLowerCase()} agregada'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _eliminarNovedad(int index) {
    setState(() {
      _novedadesActuales.removeAt(index);
    });
  }

  void _agregarVhContado() async {
    if (!_placaValidada) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Primero valida la placa del VH'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final authService = Provider.of<AuthService>(context, listen: false);

    try {
      final vhConteo = VhSegundoConteo.create(
        placa: _placaController.text,
        verificadorUid: authService.user!.uid,
        verificadorNombre: authService.userModel!.nombre,
        tieneNovedad: _tieneNovedad,
        novedades: _tieneNovedad ? List.from(_novedadesActuales) : [],
      );

      setState(() {
        _vhContados.add(vhConteo);
      });

      _limpiarFormulario();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('VH ${vhConteo.placa} agregado al conteo (${_vhContados.length} VH contados)'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Registrar error en métricas
      final metricsService = Provider.of<MetricsService>(context, listen: false);
      metricsService.recordError(
        errorType: e.runtimeType.toString(),
        screen: 'segundo_conteo_screen_agregar',
        description: 'Error al agregar VH al conteo: ${e.toString()}',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _eliminarVhContado(int index) {
    final vh = _vhContados[index];
    setState(() {
      _vhContados.removeAt(index);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('VH ${vh.placa} eliminado del conteo'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  Future<void> _finalizarSesion() async {
    if (_vhContados.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No hay VH contados para guardar'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Mostrar diálogo de confirmación
    final confirmar = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Finalizar Segundo Conteo'),
        content: Text('¿Confirmas finalizar el segundo conteo con ${_vhContados.length} VH?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );

    if (confirmar != true) return;

    // Guardar en Firebase
    if (!mounted) return;
    final firebaseService = Provider.of<FirebaseService>(context, listen: false);
    final metricsService = Provider.of<MetricsService>(context, listen: false);

    try {
      for (final vh in _vhContados) {
        await firebaseService.guardarSegundoConteo(vh);
      }

      // Registrar métricas de conteos exitosos
      final tiempoSesion = _inicioSesion != null
          ? DateTime.now().difference(_inicioSesion!)
          : const Duration(minutes: 10); // Tiempo por defecto

      for (final vh in _vhContados) {
        metricsService.recordConteo(
          vhId: vh.placa,
          productosContados: vh.novedades.length,
          tieneNovedades: vh.tieneNovedad,
          tiempoConteo: Duration(
            milliseconds: (tiempoSesion.inMilliseconds / _vhContados.length).round()
          ), // Tiempo promedio por VH
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Segundo conteo finalizado: ${_vhContados.length} VH guardados'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Registrar error en métricas
      if (mounted) {
        metricsService.recordError(
          errorType: e.runtimeType.toString(),
          screen: 'segundo_conteo_screen_finalizar',
          description: 'Error al guardar segundo conteo: ${e.toString()}',
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al guardar: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Ocultar sugerencias al tocar fuera del campo
        setState(() {
          _mostrandoSugerenciasPlaca = false;
          _placasSugeridas = [];
        });
        // Quitar foco del campo de texto
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
      appBar: AppBar(
        title: const Text('Segundo Conteo'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // Botón temporal para crear datos reales de ejemplo
          IconButton(
            onPressed: _crearDatosRealesEjemplo,
            icon: const Icon(Icons.add_circle),
            tooltip: 'Crear VH Reales de Ejemplo',
          ),
          if (_vhContados.isNotEmpty)
            IconButton(
              onPressed: _finalizarSesion,
              icon: const Icon(Icons.save),
              tooltip: 'Finalizar Conteo',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoSesion(),
              const SizedBox(height: 16),
              _buildFormularioVh(),
              if (_placaValidada) ...[
                const SizedBox(height: 16),
                _buildPredictionIA(),
                const SizedBox(height: 16),
                _buildSeccionNovedad(),
              ],
              if (_vhContados.isNotEmpty) ...[
                const SizedBox(height: 24),
                _buildListaVhContados(),
              ],
            ],
          ),
        ),
      ),
    ), // Cierre del Scaffold
    ); // Cierre del GestureDetector
  }

  Widget _buildInfoSesion() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Información de la Sesión',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Fecha', DateFormat('dd/MM/yyyy').format(DateTime.now())),
                ),
                Expanded(
                  child: _buildInfoItem('Tiempo', '00:00'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('VH Contados', '${_vhContados.length}'),
                ),
                Expanded(
                  child: _buildInfoItem('Con Novedades', '${_vhContados.where((vh) => vh.tieneNovedad).length}'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildDiferenciaVh() {
    final programados = int.tryParse(_vhProgramadosController.text) ?? 0;
    final salen = int.tryParse(_vhSalenController.text) ?? 0;
    final diferencia = salen - programados;

    Color color = Colors.grey;
    IconData icon = Icons.info;
    String mensaje = 'Sin diferencia';

    if (diferencia > 0) {
      color = Colors.orange;
      icon = Icons.trending_up;
      mensaje = '+$diferencia VH adicionales';
    } else if (diferencia < 0) {
      color = Colors.red;
      icon = Icons.trending_down;
      mensaje = '${diferencia.abs()} VH menos';
    } else if (programados > 0 && salen > 0) {
      color = Colors.green;
      icon = Icons.check_circle;
      mensaje = 'Coincide exactamente';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Text(
            mensaje,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Widget de predicción IA - FASE 2 (Avanzada)
  Widget _buildPredictionIA() {
    if (_placaController.text.isEmpty) return const SizedBox.shrink();

    return FutureBuilder<AdvancedPrediction>(
      future: context.read<AdvancedMLService>().predictAdvanced(
        vhId: _placaController.text,
        verificadorId: context.read<AuthService>().userModel?.uid ?? '',
        fechaSalida: DateTime.now(),
        ruta: null, // Se puede obtener de los datos del VH
        contextData: {
          'cantidadSkus': 10, // Estimación por defecto
          'pesoEstimado': 150.0, // Estimación por defecto
          'tiempoEstimado': 20.0,
        },
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Card(
            color: Colors.blue.shade50,
            child: const Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 12),
                  Text('🤖 Analizando patrones...'),
                ],
              ),
            ),
          );
        }

        final prediction = snapshot.data!;

        // Solo mostrar si hay probabilidad significativa
        if (prediction.probabilidadNovedad < 0.3) {
          return Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green.shade600),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '✅ IA Avanzada: Baja probabilidad de novedades',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          'Confianza: ${(prediction.confianza * 100).toInt()}%',
                          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Mostrar alerta de alta probabilidad
        return Card(
          color: Colors.orange.shade50,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.auto_awesome, color: Colors.orange.shade700),
                    const SizedBox(width: 8),
                    const Text(
                      '🚀 IA Avanzada',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade700,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'ML',
                        style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade300),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning, color: Colors.orange.shade700, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${(prediction.probabilidadNovedad * 100).toInt()}% probabilidad de ${prediction.tipoNovedadMasProbable}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                                if (prediction.riesgoOperacional > 0.5)
                                  Text(
                                    '⚠️ Riesgo operacional: ${(prediction.riesgoOperacional * 100).toInt()}%',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.red.shade600,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      if (prediction.recomendaciones.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        ...prediction.recomendaciones.take(2).map((rec) =>
                          Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(Icons.lightbulb,
                                     color: Colors.orange.shade600,
                                     size: 16),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    rec,
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Confianza: ${(prediction.confianza * 100).toInt()}%',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          if (prediction.accionesRecomendadas.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.red.shade100,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.red.shade300),
                              ),
                              child: Text(
                                '${prediction.accionesRecomendadas.length} acciones',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.red.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormularioVh() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.local_shipping, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Nuevo VH',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                      controller: _placaController,
                      decoration: InputDecoration(
                        labelText: 'Placa del VH',
                        hintText: 'Escribe para buscar...',
                        border: const OutlineInputBorder(),
                        prefixIcon: const Icon(Icons.directions_car),
                        suffixIcon: _mostrandoSugerenciasPlaca
                            ? IconButton(
                                icon: const Icon(Icons.close),
                                onPressed: () {
                                  setState(() {
                                    _mostrandoSugerenciasPlaca = false;
                                    _placasSugeridas = [];
                                  });
                                },
                                tooltip: 'Cerrar sugerencias',
                              )
                            : _placaController.text.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      _placaController.clear();
                                      setState(() {
                                        _mostrandoSugerenciasPlaca = false;
                                        _placasSugeridas = [];
                                        _placaValidada = false;
                                      });
                                    },
                                    tooltip: 'Limpiar',
                                  )
                                : const Icon(Icons.search, color: Colors.grey),
                        helperText: _placasSugeridas.isNotEmpty
                            ? '${_placasSugeridas.length} VH encontrados - Toca para seleccionar'
                            : 'Búsqueda automática mientras escribes',
                        helperStyle: TextStyle(
                          color: _placasSugeridas.isNotEmpty ? Colors.green : null,
                          fontWeight: _placasSugeridas.isNotEmpty ? FontWeight.w500 : null,
                        ),
                      ),
                      textCapitalization: TextCapitalization.characters,
                      onChanged: (value) {
                        setState(() {}); // Para actualizar el suffixIcon
                        _onPlacaChanged(value); // Búsqueda incremental con debounce
                      },
                      onFieldSubmitted: (_) => _validarPlaca(),
                      onTap: () {
                        if (_placaController.text.isNotEmpty) {
                          _buscarPlacas(_placaController.text);
                        }
                      },
                    ),
                  ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _validarPlaca,
                  icon: const Icon(Icons.search),
                  label: const Text('Validar'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            // Widget de sugerencias de placas - Implementación simple
            _buildSugerenciasPlacas(),



            // Campos de VH Programados
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _vhProgramadosController,
                    decoration: const InputDecoration(
                      labelText: 'VH Programados',
                      hintText: 'Cantidad programada',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.schedule),
                      suffixText: 'VH',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    onChanged: (value) {
                      setState(() {
                        // Actualizar UI para mostrar diferencia
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Campo requerido';
                      }
                      final cantidad = int.tryParse(value);
                      if (cantidad == null || cantidad < 0) {
                        return 'Cantidad inválida';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _vhSalenController,
                    decoration: const InputDecoration(
                      labelText: 'VH que Salen',
                      hintText: 'Cantidad real',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.exit_to_app),
                      suffixText: 'VH',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    onChanged: (value) {
                      setState(() {
                        // Actualizar UI para mostrar diferencia
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Campo requerido';
                      }
                      final cantidad = int.tryParse(value);
                      if (cantidad == null || cantidad < 0) {
                        return 'Cantidad inválida';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            // Mostrar diferencia si hay valores
            _buildDiferenciaVhCondicional(),

            // Sección validada
            _buildSeccionValidadaWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildSeccionNovedad() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  '¿Novedad?',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Sí'),
                    value: true,
                    groupValue: _tieneNovedad,
                    onChanged: (value) {
                      setState(() {
                        _tieneNovedad = value!;
                        if (!_tieneNovedad) {
                          _novedadesActuales.clear();
                        }
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('No'),
                    value: false,
                    groupValue: _tieneNovedad,
                    onChanged: (value) {
                      setState(() {
                        _tieneNovedad = value!;
                        if (!_tieneNovedad) {
                          _novedadesActuales.clear();
                        }
                      });
                    },
                  ),
                ),
              ],
            ),

            // Formulario de novedad si se seleccionó "Sí"
            if (_tieneNovedad) ...[
              const SizedBox(height: 16),
              _buildFormularioNovedad(),
            ],

            // Lista de novedades agregadas
            if (_novedadesActuales.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildListaNovedades(),
            ],

            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _agregarVhContado,
                icon: const Icon(Icons.add),
                label: const Text('Agregar VH al Conteo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormularioNovedad() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Detalles de la Novedad',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.orange.shade800,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Tipo de novedad
          Row(
            children: [
              const Text('Tipo: ', style: TextStyle(fontWeight: FontWeight.w600)),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Faltante'),
                        value: 'Faltante',
                        groupValue: _tipoNovedad,
                        onChanged: (value) {
                          setState(() {
                            _tipoNovedad = value!;
                          });
                        },
                        dense: true,
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Sobrante'),
                        value: 'Sobrante',
                        groupValue: _tipoNovedad,
                        onChanged: (value) {
                          setState(() {
                            _tipoNovedad = value!;
                          });
                        },
                        dense: true,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Campos del formulario según el súper prompt
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _dtController,
                  decoration: const InputDecoration(
                    labelText: 'DT',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _skuController,
                  decoration: const InputDecoration(
                    labelText: 'SKU',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          TextFormField(
            controller: _descripcionController,
            decoration: const InputDecoration(
              labelText: 'Descripción',
              border: OutlineInputBorder(),
              isDense: true,
            ),
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _alistadoController,
                  decoration: const InputDecoration(
                    labelText: 'Alistado',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (_) => _calcularDiferenciaNovedad(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _fisicoController,
                  decoration: const InputDecoration(
                    labelText: 'Físico',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (_) => _calcularDiferenciaNovedad(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _diferencia == 0
                        ? Colors.green.shade100
                        : _diferencia > 0
                            ? Colors.orange.shade100
                            : Colors.red.shade100,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: _diferencia == 0
                          ? Colors.green
                          : _diferencia > 0
                              ? Colors.orange
                              : Colors.red,
                    ),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Diferencia',
                        style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                      Text(
                        _diferencia.toString(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _diferencia == 0
                              ? Colors.green.shade800
                              : _diferencia > 0
                                  ? Colors.orange.shade800
                                  : Colors.red.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      controller: _verificadoController,
                      decoration: InputDecoration(
                        labelText: 'Verificado por',
                        hintText: 'Escriba el nombre del verificador',
                        border: const OutlineInputBorder(),
                        isDense: true,
                        prefixIcon: const Icon(Icons.person_search),
                        suffixIcon: _mostrandoSugerenciasVerificador
                            ? IconButton(
                                icon: const Icon(Icons.close, size: 20),
                                onPressed: () {
                                  setState(() {
                                    _mostrandoSugerenciasVerificador = false;
                                    _verificadoresSugeridos = [];
                                  });
                                },
                              )
                            : const Icon(Icons.arrow_drop_down),
                      ),
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                      onChanged: _onVerificadorChanged,
                      onTap: () {
                        if (_verificadoController.text.isNotEmpty) {
                          _buscarVerificadoresIncremental(_verificadoController.text);
                        }
                      },
                    ),
                    if (_mostrandoSugerenciasVerificador && _verificadoresSugeridos.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        constraints: const BoxConstraints(maxHeight: 150),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.3),
                              spreadRadius: 1,
                              blurRadius: 3,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _verificadoresSugeridos.length,
                          itemBuilder: (context, index) {
                            final verificador = _verificadoresSugeridos[index];
                            return ListTile(
                              dense: true,
                              leading: const Icon(Icons.person, size: 18, color: Colors.blue),
                              title: Text(
                                verificador['nombre']!,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14, // Reducido para móvil
                                ),
                              ),
                              subtitle: Text(
                                verificador['email']!,
                                style: TextStyle(
                                  fontSize: 11, // Reducido para móvil
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              onTap: () => _seleccionarVerificador(verificador['nombre']!),
                              trailing: const Icon(Icons.arrow_forward_ios, size: 14),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _armadorController,
                  decoration: const InputDecoration(
                    labelText: 'Armador',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _agregarNovedadCompleta,
                  icon: const Icon(Icons.add),
                  label: const Text('Agregar Novedad'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _limpiarFormularioNovedad,
                icon: const Icon(Icons.clear),
                label: const Text('Limpiar'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildListaNovedades() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Novedades Agregadas (${_novedadesActuales.length})',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...List.generate(_novedadesActuales.length, (index) {
          final novedad = _novedadesActuales[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: novedad.tipo == 'Faltante' ? Colors.red : Colors.orange,
                child: Text(
                  novedad.tipo[0],
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
              title: Text('${novedad.sku} - ${novedad.descripcion}'),
              subtitle: Text('DT: ${novedad.dt} | Dif: ${novedad.diferencia} | Armador: ${novedad.armador}'),
              trailing: IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () => _eliminarNovedad(index),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildListaVhContados() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.list, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'VH Contados (${_vhContados.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...List.generate(_vhContados.length, (index) {
              final vh = _vhContados[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: vh.tieneNovedad ? Colors.orange : Colors.green,
                    child: Text(
                      vh.placa.substring(0, 2),
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
                    ),
                  ),
                  title: Text(
                    vh.placa,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(
                    vh.tieneNovedad
                        ? '${vh.novedades.length} novedad(es) - ${DateFormat('HH:mm').format(vh.timestamp)}'
                        : 'Sin novedades - ${DateFormat('HH:mm').format(vh.timestamp)}',
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (vh.tieneNovedad)
                        const Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _eliminarVhContado(index),
                      ),
                    ],
                  ),
                ),
              );
            }),
            if (_vhContados.isNotEmpty) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _finalizarSesion,
                  icon: const Icon(Icons.save),
                  label: Text('Finalizar Conteo (${_vhContados.length} VH)'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Widget para mostrar sugerencias de placas
  Widget _buildSugerenciasPlacas() {
    if (!_mostrandoSugerenciasPlaca || _placasSugeridas.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      constraints: const BoxConstraints(maxHeight: 150),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _placasSugeridas.length,
        itemBuilder: (context, index) {
          final placa = _placasSugeridas[index];
          return ListTile(
            dense: true,
            leading: const Icon(Icons.local_shipping, size: 18, color: Colors.blue),
            title: Text(
              placa,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            onTap: () => _seleccionarPlaca(placa),
            trailing: const Icon(Icons.arrow_forward_ios, size: 14),
          );
        },
      ),
    );
  }

  /// Widget condicional para mostrar diferencia de VH
  Widget _buildDiferenciaVhCondicional() {
    if (_vhProgramadosController.text.isEmpty || _vhSalenController.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: _buildDiferenciaVh(),
    );
  }

  /// Widget para la sección validada
  Widget _buildSeccionValidadaWidget() {
    if (!_placaValidada) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade300),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green.shade600),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'VH ${_placaController.text} listo para conteo',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _agregarVhContado,
            icon: const Icon(Icons.add),
            label: const Text('Agregar VH al Conteo'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }
}
