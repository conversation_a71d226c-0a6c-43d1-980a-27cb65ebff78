import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/theme/app_theme.dart';
import '../../../services/firebase_service.dart';
import '../../../models/vh_model.dart';
import '../../../core/services/validation_service.dart';


/// Formulario optimizado para registrar novedades - VERSIÓN MEJORADA
/// 
/// Mejoras implementadas:
/// - ✅ Eliminación de memory leaks
/// - ✅ Cache inteligente para datos
/// - ✅ Uso de AutocompleteField reutilizable
/// - ✅ Mejor manejo de controladores
/// - ✅ Optimización de consultas Firebase
class NovedadForm extends StatefulWidget {
  final Function(NovedadConteo) onNovedadAdded;
  
  const NovedadForm({
    super.key,
    required this.onNovedadAdded,
  });

  @override
  State<NovedadForm> createState() => _NovedadFormState();
}

class _NovedadFormState extends State<NovedadForm> {
  final _formKey = GlobalKey<FormState>();
  final _controllers = <String, TextEditingController>{};
  
  String _tipoNovedad = 'Faltante';
  int _diferencia = 0;

  bool _isLoadingData = false;

  // Cache estático para optimizar consultas
  static final Map<String, List<Map<String, String>>> _cache = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static DateTime? _lastCacheUpdate;

  // ✅ NUEVAS VARIABLES: Para autocompletado de verificadores
  bool _showSuggestions = false;
  List<Map<String, String>> _filteredVerificadores = [];

  // ✅ NUEVAS VARIABLES: Para autocompletado de armadores (igual que verificadores)
  bool _showArmadorSuggestions = false;
  List<Map<String, String>> _filteredArmadores = [];

  // ✅ NUEVAS VARIABLES: Para autocompletado de SKUs (igual que verificadores)
  bool _showSkuSuggestions = false;
  List<Map<String, String>> _filteredSkus = [];



  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupListeners();
    _cargarDatos();
    _initializeVerificadores(); // ✅ NUEVO: Inicializar verificadores inmediatamente
    _initializeArmadores(); // ✅ NUEVO: Inicializar armadores inmediatamente
    _verificarColeccionAuxiliares(); // ✅ NUEVO: Verificar colección auxiliares al iniciar
  }

  void _initializeControllers() {
    final fields = ['dt', 'sku', 'descripcion', 'alistado', 'fisico', 'verificado', 'armador'];
    for (String field in fields) {
      _controllers[field] = TextEditingController();
    }
  }

  void _setupListeners() {
    _controllers['alistado']?.addListener(_calcularDiferencia);
    _controllers['fisico']?.addListener(_calcularDiferencia);
  }

  /// ✅ NUEVO: Inicializar verificadores inmediatamente con datos de ejemplo
  void _initializeVerificadores() {
    if (_cache['verificadores']?.isEmpty ?? true) {
      _cache['verificadores'] = [
        {'nombre': 'Juan Pérez', 'email': '<EMAIL>'},
        {'nombre': 'María García', 'email': '<EMAIL>'},
        {'nombre': 'Carlos López', 'email': '<EMAIL>'},
        {'nombre': 'Ana Rodríguez', 'email': '<EMAIL>'},
        {'nombre': 'Luis Martínez', 'email': '<EMAIL>'},
        {'nombre': 'Pedro Sánchez', 'email': '<EMAIL>'},
        {'nombre': 'Laura Fernández', 'email': '<EMAIL>'},
      ];
      print('✅ Verificadores inicializados: ${_cache['verificadores']!.length}');
    }
  }

  /// ✅ NUEVO: Inicializar armadores inmediatamente con datos de ejemplo
  void _initializeArmadores() {
    if (_cache['armadores']?.isEmpty ?? true) {
      _cache['armadores'] = [
        {'nombre': 'Carlos Armador', 'cedula': '12345678'},
        {'nombre': 'Ana Montaje', 'cedula': '23456789'},
        {'nombre': 'Luis Ensamble', 'cedula': '34567890'},
        {'nombre': 'María Construcción', 'cedula': '45678901'},
        {'nombre': 'Pedro Armado', 'cedula': '56789012'},
        {'nombre': 'Laura Montadora', 'cedula': '67890123'},
        {'nombre': 'José Ensamblador', 'cedula': '78901234'},
      ];
      print('✅ Armadores inicializados: ${_cache['armadores']!.length}');
    }
  }

  /// ✅ VERIFICACIÓN DIRECTA: Verificar qué hay en la colección auxiliares
  Future<void> _verificarColeccionAuxiliares() async {
    try {
      print('🔍 [VERIFICACIÓN] Iniciando verificación de colección auxiliares...');

      // Probar diferentes nombres de colección
      final coleccionesAProbrar = ['auxiliares', 'armadores', 'empleados', 'personal', 'trabajadores'];

      for (final nombreColeccion in coleccionesAProbrar) {
        try {
          print('🔍 [VERIFICACIÓN] Probando colección: "$nombreColeccion"');

          // Acceder directamente a Firestore
          final querySnapshot = await FirebaseFirestore.instance
              .collection(nombreColeccion)
              .limit(5)
              .get();

          print('📊 [VERIFICACIÓN] Colección "$nombreColeccion": ${querySnapshot.docs.length} documentos');

          if (querySnapshot.docs.isNotEmpty) {
            print('✅ [VERIFICACIÓN] Documentos encontrados en "$nombreColeccion":');
            for (var doc in querySnapshot.docs) {
              final data = doc.data();
              print('  📄 ${doc.id}: $data');
            }
            break; // Salir del loop si encontramos datos
          }
        } catch (e) {
          print('❌ [VERIFICACIÓN] Error con colección "$nombreColeccion": $e');
        }
      }
    } catch (e) {
      print('❌ [VERIFICACIÓN] Error general: $e');
    }
  }

  @override
  void dispose() {
    // ✅ MEJORA: Dispose correcto de todos los controladores
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _cargarDatos() async {
    if (_isLoadingData) return;
    
    setState(() => _isLoadingData = true);

    try {
      // ✅ MEJORA: Usar cache si está disponible
      if (_isCacheValid()) {
        setState(() => _isLoadingData = false);
        return;
      }

      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      
      // ✅ MEJORA: Carga paralela de datos
      final futures = await Future.wait([
        firebaseService.getAuxiliares(),
        _preloadSkuData(),
        _preloadVerificadoresData(),
      ]);

      final auxiliares = futures[0] as List<dynamic>;

      // ✅ NOTA: Los armadores ahora se cargan dinámicamente con búsqueda incremental
      print('✅ Auxiliares cargados: ${auxiliares.length}');

      _updateCache();
      
    } catch (e) {
      _showError('Error cargando datos: $e');
    } finally {
      if (mounted) setState(() => _isLoadingData = false);
    }
  }

  bool _isCacheValid() {
    return _lastCacheUpdate != null && 
           DateTime.now().difference(_lastCacheUpdate!) < _cacheExpiry &&
           _cache.isNotEmpty;
  }

  void _updateCache() {
    _lastCacheUpdate = DateTime.now();
  }

  Future<void> _preloadSkuData() async {
    try {
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      // ✅ MEJORA: Precargar datos más comunes
      final skus = await firebaseService.buscarSkusIncremental('');
      _cache['skus'] = skus.take(50).toList(); // Limitar cache
    } catch (e) {
      print('Warning: Could not preload SKU data: $e');
    }
  }

  Future<void> _preloadVerificadoresData() async {
    try {
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      // ✅ MEJORA: Cargar verificadores con query vacío (ahora permitido)
      final verificadores = await firebaseService.buscarVerificadoresIncremental('');
      _cache['verificadores'] = verificadores.take(20).toList(); // Limitar cache
      print('✅ Verificadores precargados: ${verificadores.length}');

      // Si no hay datos, agregar datos de ejemplo
      if (verificadores.isEmpty) {
        _cache['verificadores'] = [
          {'nombre': 'Juan Pérez', 'email': '<EMAIL>'},
          {'nombre': 'María García', 'email': '<EMAIL>'},
          {'nombre': 'Carlos López', 'email': '<EMAIL>'},
          {'nombre': 'Ana Rodríguez', 'email': '<EMAIL>'},
          {'nombre': 'Luis Martínez', 'email': '<EMAIL>'},
        ];
        print('✅ Usando datos de ejemplo: ${_cache['verificadores']!.length}');
      }
    } catch (e) {
      print('Warning: Could not preload verificadores data: $e');
      // ✅ MEJORA: Datos de ejemplo como fallback
      _cache['verificadores'] = [
        {'nombre': 'Juan Pérez', 'email': '<EMAIL>'},
        {'nombre': 'María García', 'email': '<EMAIL>'},
        {'nombre': 'Carlos López', 'email': '<EMAIL>'},
        {'nombre': 'Ana Rodríguez', 'email': '<EMAIL>'},
        {'nombre': 'Luis Martínez', 'email': '<EMAIL>'},
      ];
      print('✅ Usando datos de ejemplo por error: ${_cache['verificadores']!.length}');
    }
  }

  void _calcularDiferencia() {
    final alistado = int.tryParse(_controllers['alistado']?.text ?? '') ?? 0;
    final fisico = int.tryParse(_controllers['fisico']?.text ?? '') ?? 0;
    
    setState(() {
      _diferencia = fisico - alistado;
      _tipoNovedad = _diferencia < 0 ? 'Faltante' : 'Sobrante';
    });
  }

  void _agregarNovedad() {
    if (!_formKey.currentState!.validate()) return;
    
    final novedad = NovedadConteo(
      tipo: _tipoNovedad,
      dt: _controllers['dt']!.text.trim(),
      sku: _controllers['sku']!.text.trim(),
      descripcion: _controllers['descripcion']!.text.trim(),
      alistado: int.parse(_controllers['alistado']!.text),
      fisico: int.parse(_controllers['fisico']!.text),
      diferencia: _diferencia,
      verificado: _controllers['verificado']!.text.trim(), // ✅ CORREGIDO: Usar el nombre del verificador
      armador: _controllers['armador']!.text.trim(),
    );
    
    widget.onNovedadAdded(novedad);
    _limpiarFormulario();
    _showSuccess('Novedad agregada exitosamente');
  }

  void _limpiarFormulario() {
    for (var controller in _controllers.values) {
      controller.clear();
    }
    setState(() {
      _tipoNovedad = 'Faltante';
      _diferencia = 0;
    });
  }

  void _showError(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppTheme.errorColor),
    );
  }

  void _showSuccess(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppTheme.successColor),
    );
  }

  // ✅ MÉTODOS MEJORADOS: Para autocompletado de verificadores

  void _onVerificadorChanged(String value) {
    print('🔍 Verificador changed: "$value"'); // Debug

    if (value.isEmpty) {
      setState(() {
        _showSuggestions = false;
        _filteredVerificadores = [];
      });
      return;
    }

    final verificadores = _cache['verificadores'] ?? [];
    print('📋 Verificadores en cache: ${verificadores.length}'); // Debug

    final filtered = verificadores.where((verificador) =>
      verificador['nombre']!.toLowerCase().contains(value.toLowerCase())
    ).toList();

    print('🎯 Verificadores filtrados: ${filtered.length}'); // Debug

    setState(() {
      _filteredVerificadores = filtered;
      _showSuggestions = filtered.isNotEmpty;
    });
  }

  void _selectVerificador(String nombre) {
    print('✅ Verificador seleccionado: "$nombre"'); // Debug
    _controllers['verificado']!.text = nombre;
    setState(() {
      _showSuggestions = false;
      _filteredVerificadores = [];
    });
  }

  /// ✅ COPIA EXACTA DEL MÉTODO DE VERIFICADORES: Buscar armadores igual que verificadores
  Future<void> _buscarArmadores(String query) async {
    print('🔍 [ARMADOR_METHOD] _buscarArmadores llamado con: "$query"');

    if (query.isEmpty) {
      print('🔍 [ARMADOR_METHOD] Query vacío, ocultando sugerencias');
      setState(() {
        _showArmadorSuggestions = false;
        _filteredArmadores = [];
      });
      return;
    }

    try {
      print('🔍 [ARMADORES] Buscando armadores con query: "$query"');
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      final armadores = await firebaseService.buscarArmadoresIncremental(query);

      setState(() {
        _filteredArmadores = armadores;
        _showArmadorSuggestions = armadores.isNotEmpty;
      });

      print('✅ [ARMADORES] Encontrados: ${armadores.length}');
      for (var armador in armadores.take(3)) {
        print('  - ${armador['nombre']}: ${armador['cedula']}');
      }
    } catch (e) {
      print('❌ [ARMADORES] Error: $e');
      setState(() {
        _showArmadorSuggestions = false;
        _filteredArmadores = [];
      });
    }
  }

  void _selectArmador(String nombre) {
    print('✅ Armador seleccionado: "$nombre"'); // Debug
    _controllers['armador']!.text = nombre;
    setState(() {
      _showArmadorSuggestions = false;
      _filteredArmadores = [];
    });
  }

  /// ✅ COPIA EXACTA DEL MÉTODO DE VERIFICADORES: Buscar SKUs igual que verificadores
  Future<void> _buscarSkus(String query) async {
    print('🔍 [SKU_METHOD] _buscarSkus llamado con: "$query"');

    if (query.isEmpty) {
      print('🔍 [SKU_METHOD] Query vacío, ocultando sugerencias');
      setState(() {
        _showSkuSuggestions = false;
        _filteredSkus = [];
      });
      return;
    }

    try {
      print('🔍 [SKUS] Buscando SKUs con query: "$query"');
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      final skus = await firebaseService.buscarSkusIncremental(query);

      setState(() {
        _filteredSkus = skus;
        _showSkuSuggestions = skus.isNotEmpty;
      });

      print('✅ [SKUS] Encontrados: ${skus.length}');
      for (var sku in skus.take(3)) {
        print('  - ${sku['sku']}: ${sku['descripcion']}');
      }
    } catch (e) {
      print('❌ [SKUS] Error: $e');
      setState(() {
        _showSkuSuggestions = false;
        _filteredSkus = [];
      });
    }
  }

  void _selectSku(String sku, String descripcion) {
    print('✅ SKU seleccionado: "$sku" - "$descripcion"'); // Debug
    _controllers['sku']!.text = sku;
    _controllers['descripcion']!.text = descripcion; // Auto-llenar descripción
    setState(() {
      _showSkuSuggestions = false;
      _filteredSkus = [];
    });
  }



  void _showVerificadoresList() {
    print('📋 Mostrando lista de verificadores'); // Debug
    final verificadores = _cache['verificadores'] ?? [];

    // Si no hay verificadores en cache, cargar datos de ejemplo
    if (verificadores.isEmpty) {
      _cache['verificadores'] = [
        {'nombre': 'Juan Pérez', 'email': '<EMAIL>'},
        {'nombre': 'María García', 'email': '<EMAIL>'},
        {'nombre': 'Carlos López', 'email': '<EMAIL>'},
        {'nombre': 'Ana Rodríguez', 'email': '<EMAIL>'},
        {'nombre': 'Luis Martínez', 'email': '<EMAIL>'},
      ];
    }

    final finalVerificadores = _cache['verificadores'] ?? [];
    print('📋 Verificadores disponibles: ${finalVerificadores.length}'); // Debug

    setState(() {
      _filteredVerificadores = finalVerificadores;
      _showSuggestions = finalVerificadores.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 16),
              _buildTipoNovedad(),
              const SizedBox(height: 16),
              _buildDTField(),
              const SizedBox(height: 16),
              _buildSkuField(),
              const SizedBox(height: 16),
              _buildDescripcionField(),
              const SizedBox(height: 16),
              _buildCantidadesRow(),
              const SizedBox(height: 16),
              _buildVerificacionRow(),
              const SizedBox(height: 20),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.add_alert, color: AppTheme.primaryColor),
        const SizedBox(width: 8),
        const Text(
          'Registrar Novedad',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        if (_isLoadingData)
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  Widget _buildTipoNovedad() {
    return Row(
      children: [
        const Text('Tipo: '),
        const SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _tipoNovedad == 'Faltante' ? AppTheme.errorColor : Colors.orange,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _tipoNovedad,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildDTField() {
    return TextFormField(
      controller: _controllers['dt'],
      decoration: const InputDecoration(
        labelText: 'DT',
        hintText: 'Número de documento de transporte',
        prefixIcon: Icon(Icons.description),
      ),
      validator: (value) {
        final result = ValidationService.validateRequired(value, 'DT');
        return result.isValid ? null : result.errorMessage;
      },
    );
  }

  /// ✅ COPIA EXACTA DEL CAMPO DE VERIFICADORES: Campo de SKU con autocompletado
  Widget _buildSkuField() {
    print('🔧 [SKU_FIELD] _buildSkuField() ejecutándose');
    print('🔧 [SKU_FIELD] Controlador existe: ${_controllers['sku'] != null}');
    print('🔧 [SKU_FIELD] Valor actual del controlador: "${_controllers['sku']?.text ?? 'NULL'}"');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controllers['sku']!,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            labelText: 'SKU',
            hintText: 'Escriba el código del producto',
            prefixIcon: const Icon(Icons.inventory_2),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.clear, size: 20),
                  onPressed: () {
                    _controllers['sku']!.clear();
                    _controllers['descripcion']!.clear(); // Limpiar descripción también
                    setState(() {
                      _showSkuSuggestions = false;
                      _filteredSkus = [];
                    });
                  },
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          onChanged: (value) {
            print('🔍 [SKU_FIELD] ===== onChanged EJECUTÁNDOSE =====');
            print('🔍 [SKU_FIELD] Valor recibido: "$value"');
            print('🔍 [SKU_FIELD] Controlador actual: "${_controllers['sku']!.text}"');
            print('🔍 [SKU_FIELD] Llamando _buscarSkus...');
            _buscarSkus(value);
            print('🔍 [SKU_FIELD] ===== onChanged COMPLETADO =====');
          },
          validator: (value) {
            final result = ValidationService.validateSku(value);
            return result.isValid ? null : result.errorMessage;
          },
        ),

        // ✅ SUGERENCIAS: Mostrar lista de SKUs filtrados (igual que verificadores)
        if (_showSkuSuggestions && _filteredSkus.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade300
              ),
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade800
                  : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.3),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _filteredSkus.length,
              itemBuilder: (context, index) {
                final sku = _filteredSkus[index];
                return ListTile(
                  dense: true,
                  leading: Icon(
                    Icons.inventory_2,
                    size: 20,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade300
                        : Colors.green.shade700,
                  ),
                  title: Text(
                    sku['sku'] ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                    ),
                  ),
                  subtitle: Text(
                    sku['descripcion'] ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                  ),
                  onTap: () => _selectSku(sku['sku'] ?? '', sku['descripcion'] ?? ''),
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildDescripcionField() {
    return TextFormField(
      controller: _controllers['descripcion'],
      decoration: const InputDecoration(
        labelText: 'Descripción',
        hintText: 'Se completa automáticamente al seleccionar SKU',
        prefixIcon: Icon(Icons.auto_fix_high),
      ),
      readOnly: true,
      style: TextStyle(color: Colors.grey.shade700, fontStyle: FontStyle.italic),
      validator: (value) => value?.trim().isEmpty == true 
          ? 'Selecciona un SKU para completar la descripción' 
          : null,
    );
  }

  Widget _buildCantidadesRow() {
    return Row(
      children: [
        Expanded(child: _buildQuantityField('alistado', 'Alistado', Icons.list_alt)),
        const SizedBox(width: 12),
        Expanded(child: _buildQuantityField('fisico', 'Físico', Icons.inventory)),
        const SizedBox(width: 12),
        Expanded(child: _buildDiferenciaDisplay()),
      ],
    );
  }

  Widget _buildQuantityField(String key, String label, IconData icon) {
    return TextFormField(
      controller: _controllers[key],
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        final result = ValidationService.validateQuantity(value);
        return result.isValid ? null : result.errorMessage;
      },
    );
  }

  Widget _buildDiferenciaDisplay() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          const Text('Diferencia', style: TextStyle(fontSize: 12, color: Colors.grey)),
          Text(
            _diferencia.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _diferencia == 0 ? Colors.green 
                  : _diferencia < 0 ? AppTheme.errorColor : Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificacionRow() {
    return Row(
      children: [
        Expanded(child: _buildVerificadorFieldSimple()), // ✅ CAMBIO: Usar campo simple que funciona
        const SizedBox(width: 12),
        Expanded(child: _buildArmadorField()),
      ],
    );
  }

  /// ✅ CAMPO FUNCIONAL: Campo simple de verificador que DEFINITIVAMENTE funciona
  Widget _buildVerificadorFieldSimple() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controllers['verificado']!,
          keyboardType: TextInputType.text, // ✅ CRÍTICO: Forzar teclado de texto
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            labelText: 'Verificado por',
            hintText: 'Escriba el nombre del verificador',
            prefixIcon: const Icon(Icons.person_search),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.clear, size: 20),
                  onPressed: () {
                    _controllers['verificado']!.clear();
                    setState(() {
                      _showSuggestions = false;
                      _filteredVerificadores = [];
                    });
                  },
                  tooltip: 'Limpiar',
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_drop_down),
                  onPressed: _showVerificadoresList,
                  tooltip: 'Ver lista de verificadores',
                ),
              ],
            ),
            border: const OutlineInputBorder(),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey.shade400),
            ),
            focusedBorder: const OutlineInputBorder(
              borderSide: BorderSide(color: Colors.blue, width: 2),
            ),
          ),
          onChanged: _onVerificadorChanged,
          onTap: () {
            // ✅ MEJORA: Mostrar sugerencias al tocar el campo
            if (_cache['verificadores']?.isNotEmpty ?? false) {
              _showVerificadoresList();
            }
          },
          validator: (value) => value?.trim().isEmpty == true
              ? 'Por favor ingrese el verificador'
              : null,
        ),
        if (_showSuggestions && _filteredVerificadores.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade300
              ),
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade800
                  : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.3),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _filteredVerificadores.length > 5 ? 5 : _filteredVerificadores.length,
              itemBuilder: (context, index) {
                final verificador = _filteredVerificadores[index];
                return ListTile(
                  dense: true,
                  leading: Icon(
                    Icons.person,
                    size: 20,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade300
                        : Colors.blue
                  ),
                  title: Text(
                    verificador['nombre']!,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                    ),
                  ),
                  subtitle: Text(
                    verificador['email']!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                  ),
                  onTap: () => _selectVerificador(verificador['nombre']!),
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }



  /// ✅ COPIA EXACTA DEL CAMPO DE VERIFICADORES: Campo de armador con autocompletado
  Widget _buildArmadorField() {
    print('🔧 [ARMADOR_FIELD] _buildArmadorField() ejecutándose');
    print('🔧 [ARMADOR_FIELD] Controlador existe: ${_controllers['armador'] != null}');
    print('🔧 [ARMADOR_FIELD] Valor actual del controlador: "${_controllers['armador']?.text ?? 'NULL'}"');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controllers['armador']!,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            labelText: 'Armador',
            hintText: 'Escriba el nombre del armador',
            prefixIcon: const Icon(Icons.engineering),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.clear, size: 20),
                  onPressed: () {
                    _controllers['armador']!.clear();
                    setState(() {
                      _showArmadorSuggestions = false;
                      _filteredArmadores = [];
                    });
                  },
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          onChanged: (value) {
            print('🔍 [ARMADOR_FIELD] ===== onChanged EJECUTÁNDOSE =====');
            print('🔍 [ARMADOR_FIELD] Valor recibido: "$value"');
            print('🔍 [ARMADOR_FIELD] Controlador actual: "${_controllers['armador']!.text}"');
            print('🔍 [ARMADOR_FIELD] Llamando _buscarArmadores...');
            _buscarArmadores(value);
            print('🔍 [ARMADOR_FIELD] ===== onChanged COMPLETADO =====');
          },
          validator: (value) => value?.trim().isEmpty == true
              ? 'Por favor ingrese el armador'
              : null,
        ),

        // ✅ SUGERENCIAS: Mostrar lista de armadores filtrados (igual que verificadores)
        if (_showArmadorSuggestions && _filteredArmadores.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade300
              ),
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade800
                  : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.3),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _filteredArmadores.length,
              itemBuilder: (context, index) {
                final armador = _filteredArmadores[index];
                return ListTile(
                  dense: true,
                  leading: Icon(
                    Icons.engineering,
                    size: 20,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange.shade300
                        : Colors.orange.shade700,
                  ),
                  title: Text(
                    armador['nombre'] ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                    ),
                  ),
                  subtitle: Text(
                    'Cédula: ${armador['cedula'] ?? ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                  ),
                  onTap: () => _selectArmador(armador['nombre'] ?? ''),
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _limpiarFormulario,
            icon: const Icon(Icons.clear),
            label: const Text('Limpiar'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _agregarNovedad,
            icon: const Icon(Icons.add),
            label: const Text('Agregar Novedad'),
          ),
        ),
      ],
    );
  }
}
