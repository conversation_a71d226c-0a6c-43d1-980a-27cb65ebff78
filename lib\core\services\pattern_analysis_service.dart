import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../models/pattern_analysis_model.dart';
import '../utils/logger.dart';

/// Servicio para análisis de patrones e IA básica
/// FASE 1: Implementación de algoritmos básicos de detección de patrones
class PatternAnalysisService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Cache de datos para análisis
  List<PatternAnalysisData> _cachedData = [];
  final List<DetectedPattern> _detectedPatterns = [];
  DateTime? _lastAnalysis;
  
  // Getters
  List<DetectedPattern> get detectedPatterns => _detectedPatterns;
  bool get hasRecentAnalysis => _lastAnalysis != null && 
      DateTime.now().difference(_lastAnalysis!).inHours < 6;

  /// Inicializar el servicio
  Future<void> initialize() async {
    try {
      Logger.info('🤖 [PATTERN_AI] Inicializando servicio de análisis de patrones...');
      
      // Cargar datos históricos
      await _loadHistoricalData();
      
      // Ejecutar análisis inicial
      await _runBasicAnalysis();
      
      Logger.info('✅ [PATTERN_AI] Servicio inicializado correctamente');
    } catch (e, stackTrace) {
      Logger.error('❌ [PATTERN_AI] Error inicializando servicio', e, stackTrace);
    }
  }

  /// Cargar datos históricos de Firebase
  Future<void> _loadHistoricalData() async {
    try {
      Logger.info('📊 [PATTERN_AI] Cargando datos históricos...');
      
      // Obtener últimos 90 días de datos
      final cutoffDate = DateTime.now().subtract(const Duration(days: 90));
      
      final querySnapshot = await _firestore
          .collection('segundos_conteos')
          .where('fecha', isGreaterThan: Timestamp.fromDate(cutoffDate))
          .orderBy('fecha', descending: true)
          .limit(1000)
          .get();

      _cachedData = querySnapshot.docs
          .map((doc) => PatternAnalysisData.fromSegundoConteo({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();

      Logger.info('📈 [PATTERN_AI] Cargados ${_cachedData.length} registros para análisis');
    } catch (e, stackTrace) {
      Logger.error('❌ [PATTERN_AI] Error cargando datos históricos', e, stackTrace);
    }
  }

  /// Ejecutar análisis básico de patrones
  Future<void> _runBasicAnalysis() async {
    try {
      Logger.info('🔍 [PATTERN_AI] Ejecutando análisis básico de patrones...');
      
      _detectedPatterns.clear();
      
      // Análisis 1: Patrones temporales
      _detectedPatterns.addAll(await _analyzeTemporalPatterns());
      
      // Análisis 2: Patrones por verificador
      _detectedPatterns.addAll(await _analyzeVerificadorPatterns());
      
      // Análisis 3: Patrones por SKU
      _detectedPatterns.addAll(await _analyzeSkuPatterns());
      
      // Análisis 4: Patrones por VH/Ruta
      _detectedPatterns.addAll(await _analyzeVhPatterns());
      
      _lastAnalysis = DateTime.now();
      
      Logger.info('✅ [PATTERN_AI] Análisis completado. ${_detectedPatterns.length} patrones detectados');
      notifyListeners();
      
    } catch (e, stackTrace) {
      Logger.error('❌ [PATTERN_AI] Error en análisis de patrones', e, stackTrace);
    }
  }

  /// Análisis de patrones temporales (día, hora)
  Future<List<DetectedPattern>> _analyzeTemporalPatterns() async {
    final patterns = <DetectedPattern>[];
    
    if (_cachedData.isEmpty) return patterns;
    
    // Análisis por día de la semana
    final novedadesPorDia = <int, int>{};
    final totalPorDia = <int, int>{};
    
    for (final data in _cachedData) {
      totalPorDia[data.diaSemana] = (totalPorDia[data.diaSemana] ?? 0) + 1;
      if (data.tieneNovedad) {
        novedadesPorDia[data.diaSemana] = (novedadesPorDia[data.diaSemana] ?? 0) + 1;
      }
    }
    
    // Detectar días con alta incidencia de novedades
    for (final dia in novedadesPorDia.keys) {
      final porcentaje = novedadesPorDia[dia]! / totalPorDia[dia]!;
      if (porcentaje > 0.3) { // Más del 30% de novedades
        final nombreDia = _getNombreDia(dia);
        patterns.add(DetectedPattern(
          id: 'temporal_dia_$dia',
          tipo: 'temporal',
          descripcion: '$nombreDia tiene ${(porcentaje * 100).toInt()}% más novedades que el promedio',
          frecuencia: porcentaje,
          impacto: _calculateImpact(porcentaje, totalPorDia[dia]!),
          fechaDeteccion: DateTime.now(),
          detalles: {
            'dia': dia,
            'nombreDia': nombreDia,
            'novedades': novedadesPorDia[dia],
            'total': totalPorDia[dia],
            'porcentaje': porcentaje,
          },
        ));
      }
    }
    
    // Análisis por hora del día
    final novedadesPorHora = <int, int>{};
    final totalPorHora = <int, int>{};
    
    for (final data in _cachedData) {
      totalPorHora[data.hora] = (totalPorHora[data.hora] ?? 0) + 1;
      if (data.tieneNovedad) {
        novedadesPorHora[data.hora] = (novedadesPorHora[data.hora] ?? 0) + 1;
      }
    }
    
    // Detectar horas pico de novedades
    for (final hora in novedadesPorHora.keys) {
      final porcentaje = novedadesPorHora[hora]! / totalPorHora[hora]!;
      if (porcentaje > 0.4 && totalPorHora[hora]! > 5) { // Más del 40% y mínimo 5 registros
        patterns.add(DetectedPattern(
          id: 'temporal_hora_$hora',
          tipo: 'temporal',
          descripcion: 'Entre $hora:00-${hora + 1}:00 hay ${(porcentaje * 100).toInt()}% más novedades',
          frecuencia: porcentaje,
          impacto: _calculateImpact(porcentaje, totalPorHora[hora]!),
          fechaDeteccion: DateTime.now(),
          detalles: {
            'hora': hora,
            'novedades': novedadesPorHora[hora],
            'total': totalPorHora[hora],
            'porcentaje': porcentaje,
          },
        ));
      }
    }
    
    return patterns;
  }

  /// Análisis de patrones por verificador
  Future<List<DetectedPattern>> _analyzeVerificadorPatterns() async {
    final patterns = <DetectedPattern>[];
    
    if (_cachedData.isEmpty) return patterns;
    
    final verificadorStats = <String, Map<String, dynamic>>{};
    
    // Recopilar estadísticas por verificador
    for (final data in _cachedData) {
      final verificadorId = data.verificadorId;
      if (!verificadorStats.containsKey(verificadorId)) {
        verificadorStats[verificadorId] = {
          'total': 0,
          'novedades': 0,
          'tiempoTotal': 0,
          'skusTotal': 0,
        };
      }
      
      final stats = verificadorStats[verificadorId]!;
      stats['total'] = stats['total'] + 1;
      if (data.tieneNovedad) stats['novedades'] = stats['novedades'] + 1;
      stats['tiempoTotal'] = stats['tiempoTotal'] + data.tiempoConteoMinutos;
      stats['skusTotal'] = stats['skusTotal'] + data.cantidadSkus;
    }
    
    // Analizar cada verificador
    for (final entry in verificadorStats.entries) {
      final verificadorId = entry.key;
      final stats = entry.value;
      
      if (stats['total'] < 10) continue; // Mínimo 10 conteos para análisis
      
      final porcentajeNovedades = stats['novedades'] / stats['total'];
      final tiempoPromedio = stats['tiempoTotal'] / stats['total'];
      final skusPromedio = stats['skusTotal'] / stats['total'];
      
      // Detectar verificadores con alta precisión
      if (porcentajeNovedades < 0.1) { // Menos del 10% de novedades
        patterns.add(DetectedPattern(
          id: 'verificador_precision_$verificadorId',
          tipo: 'verificador',
          descripcion: 'Verificador tiene ${(100 - porcentajeNovedades * 100).toInt()}% de precisión',
          frecuencia: (1 - porcentajeNovedades).toDouble(),
          impacto: _calculateImpact((1 - porcentajeNovedades).toDouble(), stats['total'] as int),
          fechaDeteccion: DateTime.now(),
          detalles: {
            'verificadorId': verificadorId,
            'porcentajeNovedades': porcentajeNovedades,
            'tiempoPromedio': tiempoPromedio,
            'skusPromedio': skusPromedio,
            'totalConteos': stats['total'],
          },
        ));
      }
      
      // Detectar verificadores con problemas
      if (porcentajeNovedades > 0.3) { // Más del 30% de novedades
        patterns.add(DetectedPattern(
          id: 'verificador_problema_$verificadorId',
          tipo: 'verificador',
          descripcion: 'Verificador tiene ${(porcentajeNovedades * 100).toInt()}% de novedades - Necesita entrenamiento',
          frecuencia: porcentajeNovedades,
          impacto: _calculateImpact(porcentajeNovedades, stats['total']),
          fechaDeteccion: DateTime.now(),
          detalles: {
            'verificadorId': verificadorId,
            'porcentajeNovedades': porcentajeNovedades,
            'tiempoPromedio': tiempoPromedio,
            'recomendacion': 'Entrenamiento adicional requerido',
          },
        ));
      }
    }
    
    return patterns;
  }

  /// Análisis de patrones por SKU
  Future<List<DetectedPattern>> _analyzeSkuPatterns() async {
    final patterns = <DetectedPattern>[];
    
    final skuProblemas = <String, int>{};
    
    // Contar SKUs con problemas
    for (final data in _cachedData) {
      for (final sku in data.skusConNovedad) {
        skuProblemas[sku] = (skuProblemas[sku] ?? 0) + 1;
      }
    }
    
    // Detectar SKUs problemáticos
    for (final entry in skuProblemas.entries) {
      if (entry.value > 5) { // Más de 5 incidencias
        patterns.add(DetectedPattern(
          id: 'sku_problema_${entry.key}',
          tipo: 'sku',
          descripcion: 'SKU ${entry.key} tiene ${entry.value} novedades recurrentes',
          frecuencia: entry.value / _cachedData.length,
          impacto: _calculateImpact(entry.value / _cachedData.length, entry.value),
          fechaDeteccion: DateTime.now(),
          detalles: {
            'sku': entry.key,
            'incidencias': entry.value,
            'recomendacion': 'Revisar proceso de alistamiento para este SKU',
          },
        ));
      }
    }
    
    return patterns;
  }

  /// Análisis de patrones por VH/Ruta
  Future<List<DetectedPattern>> _analyzeVhPatterns() async {
    final patterns = <DetectedPattern>[];
    
    final rutaStats = <String, Map<String, int>>{};
    
    // Recopilar estadísticas por ruta
    for (final data in _cachedData) {
      if (data.ruta == null) continue;
      
      if (!rutaStats.containsKey(data.ruta)) {
        rutaStats[data.ruta!] = {'total': 0, 'novedades': 0};
      }
      
      rutaStats[data.ruta!]!['total'] = rutaStats[data.ruta!]!['total']! + 1;
      if (data.tieneNovedad) {
        rutaStats[data.ruta!]!['novedades'] = rutaStats[data.ruta!]!['novedades']! + 1;
      }
    }
    
    // Analizar rutas problemáticas
    for (final entry in rutaStats.entries) {
      final ruta = entry.key;
      final stats = entry.value;
      
      if (stats['total']! < 5) continue; // Mínimo 5 VH para análisis
      
      final porcentaje = stats['novedades']! / stats['total']!;
      if (porcentaje > 0.25) { // Más del 25% de novedades
        patterns.add(DetectedPattern(
          id: 'ruta_problema_$ruta',
          tipo: 'vh',
          descripcion: 'Ruta $ruta tiene ${(porcentaje * 100).toInt()}% de novedades',
          frecuencia: porcentaje,
          impacto: _calculateImpact(porcentaje, stats['total']!),
          fechaDeteccion: DateTime.now(),
          detalles: {
            'ruta': ruta,
            'novedades': stats['novedades'],
            'total': stats['total'],
            'porcentaje': porcentaje,
            'recomendacion': 'Revisar proceso de carga para esta ruta',
          },
        ));
      }
    }
    
    return patterns;
  }

  /// Calcular impacto de un patrón
  double _calculateImpact(double frecuencia, int volumen) {
    // Fórmula simple: frecuencia * log(volumen) / 10
    return min(1.0, frecuencia * (log(volumen + 1) / log(10)) / 10);
  }

  /// Obtener nombre del día
  String _getNombreDia(int dia) {
    const nombres = ['', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'];
    return nombres[dia];
  }

  /// Obtener predicción básica para un VH
  Future<PatternPrediction> predictNovedad({
    required String vhId,
    required String verificadorId,
    String? ruta,
    DateTime? fechaSalida,
  }) async {
    try {
      fechaSalida ??= DateTime.now();
      
      // Análisis básico basado en patrones detectados
      double probabilidad = 0.1; // Base 10%
      final recomendaciones = <String>[];
      final factores = <String, dynamic>{};
      
      // Factor día de la semana
      final diaPattern = _detectedPatterns
          .where((p) => p.tipo == 'temporal' && p.detalles['dia'] == fechaSalida!.weekday)
          .firstOrNull;
      
      if (diaPattern != null) {
        probabilidad += diaPattern.frecuencia * 0.3;
        factores['dia_semana'] = diaPattern.frecuencia;
        recomendaciones.add(diaPattern.descripcion);
      }
      
      // Factor hora
      final horaPattern = _detectedPatterns
          .where((p) => p.tipo == 'temporal' && p.detalles['hora'] == fechaSalida!.hour)
          .firstOrNull;
      
      if (horaPattern != null) {
        probabilidad += horaPattern.frecuencia * 0.2;
        factores['hora'] = horaPattern.frecuencia;
        recomendaciones.add('Hora pico de novedades detectada');
      }
      
      // Factor verificador
      final verificadorPattern = _detectedPatterns
          .where((p) => p.tipo == 'verificador' && p.detalles['verificadorId'] == verificadorId)
          .firstOrNull;
      
      if (verificadorPattern != null) {
        if (verificadorPattern.id.contains('precision')) {
          probabilidad *= 0.5; // Reducir probabilidad para verificadores precisos
          factores['verificador'] = 'alta_precision';
          recomendaciones.add('Verificador con alta precisión histórica');
        } else {
          probabilidad += verificadorPattern.frecuencia * 0.4;
          factores['verificador'] = 'necesita_atencion';
          recomendaciones.add('Verificador requiere atención especial');
        }
      }
      
      // Factor ruta
      if (ruta != null) {
        final rutaPattern = _detectedPatterns
            .where((p) => p.tipo == 'vh' && p.detalles['ruta'] == ruta)
            .firstOrNull;
        
        if (rutaPattern != null) {
          probabilidad += rutaPattern.frecuencia * 0.3;
          factores['ruta'] = rutaPattern.frecuencia;
          recomendaciones.add('Ruta con historial de novedades');
        }
      }
      
      // Limitar probabilidad entre 0 y 1
      probabilidad = min(1.0, max(0.0, probabilidad));
      
      // Determinar tipo más probable
      String tipoMasProbable = 'Faltante';
      if (probabilidad > 0.6) {
        // Analizar histórico para determinar tipo más común
        final faltantes = _cachedData.where((d) => d.tipoNovedad == 'Faltante').length;
        final sobrantes = _cachedData.where((d) => d.tipoNovedad == 'Sobrante').length;
        tipoMasProbable = faltantes > sobrantes ? 'Faltante' : 'Sobrante';
      }
      
      return PatternPrediction(
        probabilidadNovedad: probabilidad,
        tipoNovedadMasProbable: tipoMasProbable,
        confianza: min(0.8, _cachedData.length / 100), // Confianza basada en datos disponibles
        recomendaciones: recomendaciones,
        factoresInfluencia: factores,
      );
      
    } catch (e, stackTrace) {
      Logger.error('❌ [PATTERN_AI] Error en predicción', e, stackTrace);
      
      // Predicción por defecto
      return const PatternPrediction(
        probabilidadNovedad: 0.1,
        tipoNovedadMasProbable: 'Faltante',
        confianza: 0.1,
        recomendaciones: ['Análisis no disponible'],
        factoresInfluencia: {},
      );
    }
  }

  /// Forzar nuevo análisis
  Future<void> forceAnalysis() async {
    await _loadHistoricalData();
    await _runBasicAnalysis();
  }

  /// Obtener resumen de patrones
  Map<String, dynamic> getPatternSummary() {
    final summary = <String, dynamic>{
      'totalPatrones': _detectedPatterns.length,
      'ultimoAnalisis': _lastAnalysis?.toIso8601String(),
      'datosAnalizados': _cachedData.length,
      'patronesPorTipo': <String, int>{},
    };
    
    for (final pattern in _detectedPatterns) {
      summary['patronesPorTipo'][pattern.tipo] = 
          (summary['patronesPorTipo'][pattern.tipo] ?? 0) + 1;
    }
    
    return summary;
  }
}
