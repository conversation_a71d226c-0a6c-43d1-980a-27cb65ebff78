// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in recount_pro/test/test_helpers/mock_services.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:ui' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;
import 'package:recount_pro/core/exceptions/app_exceptions.dart' as _i9;
import 'package:recount_pro/core/repositories/conteo_repository.dart' as _i11;
import 'package:recount_pro/core/repositories/sku_repository.dart' as _i12;
import 'package:recount_pro/core/services/app_state_service.dart' as _i8;
import 'package:recount_pro/models/auxiliar_model.dart' as _i13;
import 'package:recount_pro/models/sku_model.dart' as _i7;
import 'package:recount_pro/models/vh_model.dart' as _i6;
import 'package:recount_pro/services/auth_service.dart' as _i2;
import 'package:recount_pro/services/firebase_service.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i2.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<bool> signInWithEmailAndPassword(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmailAndPassword,
          [
            email,
            password,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> createUserWithEmailAndPassword(
    String? email,
    String? password,
    String? nombre,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #createUserWithEmailAndPassword,
          [
            email,
            password,
            nombre,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> checkAuthState() => (super.noSuchMethod(
        Invocation.method(
          #checkAuthState,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  void addListener(_i4.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i4.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [FirebaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseService extends _i1.Mock implements _i5.FirebaseService {
  MockFirebaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get vhContadosHoy => (super.noSuchMethod(
        Invocation.getter(#vhContadosHoy),
        returnValue: 0,
      ) as int);

  @override
  int get vhProgramadosHoy => (super.noSuchMethod(
        Invocation.getter(#vhProgramadosHoy),
        returnValue: 0,
      ) as int);

  @override
  int get erroresDelMes => (super.noSuchMethod(
        Invocation.getter(#erroresDelMes),
        returnValue: 0,
      ) as int);

  @override
  double get porcentajeAvance => (super.noSuchMethod(
        Invocation.getter(#porcentajeAvance),
        returnValue: 0.0,
      ) as double);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<List<_i6.VhProgramado>> getVhProgramadosHoy() =>
      (super.noSuchMethod(
        Invocation.method(
          #getVhProgramadosHoy,
          [],
        ),
        returnValue:
            _i3.Future<List<_i6.VhProgramado>>.value(<_i6.VhProgramado>[]),
      ) as _i3.Future<List<_i6.VhProgramado>>);

  @override
  _i3.Future<List<_i6.ConteoVh>> getConteosHoy(String? verificadorUid) =>
      (super.noSuchMethod(
        Invocation.method(
          #getConteosHoy,
          [verificadorUid],
        ),
        returnValue: _i3.Future<List<_i6.ConteoVh>>.value(<_i6.ConteoVh>[]),
      ) as _i3.Future<List<_i6.ConteoVh>>);

  @override
  _i3.Future<bool> guardarConteo(_i6.ConteoVh? conteo) => (super.noSuchMethod(
        Invocation.method(
          #guardarConteo,
          [conteo],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<List<_i7.SkuModel>> getSKUs() => (super.noSuchMethod(
        Invocation.method(
          #getSKUs,
          [],
        ),
        returnValue: _i3.Future<List<_i7.SkuModel>>.value(<_i7.SkuModel>[]),
      ) as _i3.Future<List<_i7.SkuModel>>);

  @override
  _i3.Future<_i7.SkuModel?> buscarSKU(String? skuCode) => (super.noSuchMethod(
        Invocation.method(
          #buscarSKU,
          [skuCode],
        ),
        returnValue: _i3.Future<_i7.SkuModel?>.value(),
      ) as _i3.Future<_i7.SkuModel?>);

  @override
  _i3.Future<List<_i13.AuxiliarModel>> getAuxiliares() => (super.noSuchMethod(
        Invocation.method(
          #getAuxiliares,
          [],
        ),
        returnValue:
            _i3.Future<List<_i13.AuxiliarModel>>.value(<_i13.AuxiliarModel>[]),
      ) as _i3.Future<List<_i13.AuxiliarModel>>);

  @override
  _i3.Future<void> cargarEstadisticas(String? verificadorUid) =>
      (super.noSuchMethod(
        Invocation.method(
          #cargarEstadisticas,
          [verificadorUid],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> vhYaContado(
    String? vhId,
    String? verificadorUid,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #vhYaContado,
          [
            vhId,
            verificadorUid,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<_i6.VhProgramado?> getVhPorPlaca(String? placa) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVhPorPlaca,
          [placa],
        ),
        returnValue: _i3.Future<_i6.VhProgramado?>.value(),
      ) as _i3.Future<_i6.VhProgramado?>);

  @override
  void addListener(_i4.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i4.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [AppStateService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppStateService extends _i1.Mock implements _i8.AppStateService {
  MockAppStateService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.LoadingState get loadingState => (super.noSuchMethod(
        Invocation.getter(#loadingState),
        returnValue: _i8.LoadingState.idle,
      ) as _i8.LoadingState);

  @override
  bool get hasError => (super.noSuchMethod(
        Invocation.getter(#hasError),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isAuthenticating => (super.noSuchMethod(
        Invocation.getter(#isAuthenticating),
        returnValue: false,
      ) as bool);

  @override
  bool get isSavingConteo => (super.noSuchMethod(
        Invocation.getter(#isSavingConteo),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingVh => (super.noSuchMethod(
        Invocation.getter(#isLoadingVh),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingSkus => (super.noSuchMethod(
        Invocation.getter(#isLoadingSkus),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoadingEstadisticas => (super.noSuchMethod(
        Invocation.getter(#isLoadingEstadisticas),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  void setLoadingState(
    _i8.LoadingState? state, {
    String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #setLoadingState,
          [state],
          {#message: message},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setError(_i9.AppException? error) => super.noSuchMethod(
        Invocation.method(
          #setError,
          [error],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clearError() => super.noSuchMethod(
        Invocation.method(
          #clearError,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setAuthenticating(
    bool? isAuthenticating, {
    String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #setAuthenticating,
          [isAuthenticating],
          {#message: message},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setSavingConteo(
    bool? isSaving, {
    String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #setSavingConteo,
          [isSaving],
          {#message: message},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setLoadingVh(
    bool? isLoading, {
    String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #setLoadingVh,
          [isLoading],
          {#message: message},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setLoadingSkus(
    bool? isLoading, {
    String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #setLoadingSkus,
          [isLoading],
          {#message: message},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setLoadingEstadisticas(
    bool? isLoading, {
    String? message,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #setLoadingEstadisticas,
          [isLoading],
          {#message: message},
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.Future<T?> executeWithState<T>({
    required _i3.Future<T> Function()? operation,
    required String? loadingMessage,
    String? successMessage,
    bool? showSuccess = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #executeWithState,
          [],
          {
            #operation: operation,
            #loadingMessage: loadingMessage,
            #successMessage: successMessage,
            #showSuccess: showSuccess,
          },
        ),
        returnValue: _i3.Future<T?>.value(),
      ) as _i3.Future<T?>);

  @override
  void reset() => super.noSuchMethod(
        Invocation.method(
          #reset,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool canPerformOperation() => (super.noSuchMethod(
        Invocation.method(
          #canPerformOperation,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  String getStateDescription() => (super.noSuchMethod(
        Invocation.method(
          #getStateDescription,
          [],
        ),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.method(
            #getStateDescription,
            [],
          ),
        ),
      ) as String);

  @override
  void addListener(_i4.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i4.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ConteoRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockConteoRepository extends _i1.Mock implements _i11.ConteoRepository {
  MockConteoRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i6.VhProgramado>> getVhProgramados({DateTime? fecha}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVhProgramados,
          [],
          {#fecha: fecha},
        ),
        returnValue:
            _i3.Future<List<_i6.VhProgramado>>.value(<_i6.VhProgramado>[]),
      ) as _i3.Future<List<_i6.VhProgramado>>);

  @override
  _i3.Future<_i6.VhProgramado?> getVhPorPlaca(String? placa) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVhPorPlaca,
          [placa],
        ),
        returnValue: _i3.Future<_i6.VhProgramado?>.value(),
      ) as _i3.Future<_i6.VhProgramado?>);

  @override
  _i3.Future<bool> vhYaContado(
    String? vhId,
    String? verificadorUid,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #vhYaContado,
          [
            vhId,
            verificadorUid,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<String> guardarConteo(_i6.ConteoVh? conteo) => (super.noSuchMethod(
        Invocation.method(
          #guardarConteo,
          [conteo],
        ),
        returnValue: _i3.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #guardarConteo,
            [conteo],
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<List<_i6.ConteoVh>> getConteosPorVerificador(
    String? verificadorUid, {
    DateTime? fecha,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getConteosPorVerificador,
          [verificadorUid],
          {#fecha: fecha},
        ),
        returnValue: _i3.Future<List<_i6.ConteoVh>>.value(<_i6.ConteoVh>[]),
      ) as _i3.Future<List<_i6.ConteoVh>>);

  @override
  _i3.Future<Map<String, int>> getEstadisticasVerificador(
          String? verificadorUid) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEstadisticasVerificador,
          [verificadorUid],
        ),
        returnValue: _i3.Future<Map<String, int>>.value(<String, int>{}),
      ) as _i3.Future<Map<String, int>>);
}

/// A class which mocks [SkuRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSkuRepository extends _i1.Mock implements _i12.SkuRepository {
  MockSkuRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i7.SkuModel>> getAllSkus() => (super.noSuchMethod(
        Invocation.method(
          #getAllSkus,
          [],
        ),
        returnValue: _i3.Future<List<_i7.SkuModel>>.value(<_i7.SkuModel>[]),
      ) as _i3.Future<List<_i7.SkuModel>>);

  @override
  _i3.Future<_i7.SkuModel?> getSkuByCodigo(String? codigo) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSkuByCodigo,
          [codigo],
        ),
        returnValue: _i3.Future<_i7.SkuModel?>.value(),
      ) as _i3.Future<_i7.SkuModel?>);

  @override
  _i3.Future<List<_i7.SkuModel>> searchSkusByDescripcion(String? descripcion) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchSkusByDescripcion,
          [descripcion],
        ),
        returnValue: _i3.Future<List<_i7.SkuModel>>.value(<_i7.SkuModel>[]),
      ) as _i3.Future<List<_i7.SkuModel>>);

  @override
  _i3.Future<List<_i7.SkuModel>> getSkusByCategoria(String? categoria) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSkusByCategoria,
          [categoria],
        ),
        returnValue: _i3.Future<List<_i7.SkuModel>>.value(<_i7.SkuModel>[]),
      ) as _i3.Future<List<_i7.SkuModel>>);

  @override
  _i3.Future<List<String>> getCategorias() => (super.noSuchMethod(
        Invocation.method(
          #getCategorias,
          [],
        ),
        returnValue: _i3.Future<List<String>>.value(<String>[]),
      ) as _i3.Future<List<String>>);

  @override
  _i3.Future<bool> skuExists(String? codigo) => (super.noSuchMethod(
        Invocation.method(
          #skuExists,
          [codigo],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);
}

/// A class which mocks [AuxiliarRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuxiliarRepository extends _i1.Mock
    implements _i12.AuxiliarRepository {
  MockAuxiliarRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i12.AuxiliarModel>> getAuxiliaresActivos() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAuxiliaresActivos,
          [],
        ),
        returnValue:
            _i3.Future<List<_i12.AuxiliarModel>>.value(<_i12.AuxiliarModel>[]),
      ) as _i3.Future<List<_i12.AuxiliarModel>>);

  @override
  _i3.Future<List<_i12.AuxiliarModel>> getAuxiliaresPorCargo(String? cargo) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAuxiliaresPorCargo,
          [cargo],
        ),
        returnValue:
            _i3.Future<List<_i12.AuxiliarModel>>.value(<_i12.AuxiliarModel>[]),
      ) as _i3.Future<List<_i12.AuxiliarModel>>);
}
