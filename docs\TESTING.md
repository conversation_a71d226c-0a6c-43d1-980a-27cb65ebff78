# Documentación de Testing - ReCount Pro

## 📋 Índice

1. [Estrategia de Testing](#estrategia-de-testing)
2. [Configuración](#configuración)
3. [Tests Unitarios](#tests-unitarios)
4. [Tests de Widgets](#tests-de-widgets)
5. [Tests de Integración](#tests-de-integración)
6. [Mocks y Stubs](#mocks-y-stubs)
7. [Cobertura de Código](#cobertura-de-código)
8. [CI/CD Testing](#cicd-testing)

## 🎯 Estrategia de Testing

### Pirámide de Testing

```
        /\
       /  \
      / UI \     ← Integration Tests (Pocos, Lentos, Costosos)
     /______\
    /        \
   / Widget   \   ← Widget Tests (Algunos, Medios)
  /____________\
 /              \
/ Unit Tests     \ ← Unit Tests (Muchos, Rápidos, Baratos)
/________________\
```

### Objetivos de Testing

- **Cobertura mínima**: 80% para código crítico
- **Confiabilidad**: Tests determinísticos y estables
- **Velocidad**: Suite de tests que ejecute en < 5 minutos
- **Mantenibilidad**: Tests fáciles de entender y modificar

### Tipos de Tests Implementados

| Tipo | Cantidad | Cobertura | Tiempo Ejecución |
|------|----------|-----------|------------------|
| Unit Tests | 62 | 95% | ~30 segundos |
| Widget Tests | 8 | 80% | ~45 segundos |
| Integration Tests | 7 | 70% | ~2 minutos |
| **Total** | **77** | **85%** | **~3 minutos** |

## ⚙️ Configuración

### Dependencias de Testing

```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.4
  build_runner: ^2.4.7
  test: ^1.24.9
  fake_async: ^1.3.1
```

### Estructura de Directorios

```
test/
├── core/
│   ├── services/
│   │   ├── analytics_service_test.dart
│   │   ├── app_state_service_test.dart
│   │   ├── cache_service_test.dart
│   │   ├── localization_service_test.dart
│   │   ├── metrics_service_test.dart
│   │   ├── performance_service_test.dart
│   │   ├── theme_service_test.dart
│   │   └── validation_service_test.dart
│   └── utils/
│       ├── error_handler_test.dart
│       └── logger_test.dart
├── models/
│   ├── user_model_test.dart
│   └── vh_model_test.dart
├── features/
│   └── auth/
│       └── login_screen_test.dart
├── test_helpers/
│   ├── mock_services.dart
│   ├── test_data.dart
│   └── test_utils.dart
└── integration_test/
    └── app_test.dart
```

### Configuración de Mocks

```dart
// test/test_helpers/mock_services.dart
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:recount_pro/services/auth_service.dart';
import 'package:recount_pro/services/firebase_service.dart';

// Generar mocks automáticamente
@GenerateMocks([
  AuthService,
  FirebaseService,
])
void main() {}

// Ejecutar: flutter packages pub run build_runner build
```

## 🧪 Tests Unitarios

### Ejemplo: Testing de Servicios

```dart
// test/core/services/validation_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:recount_pro/core/services/validation_service.dart';
import 'package:recount_pro/core/exceptions/app_exceptions.dart';

void main() {
  group('ValidationService', () {
    group('validateEmail', () {
      test('should return valid result for correct email format', () {
        // Arrange
        const email = '<EMAIL>';
        
        // Act
        final result = ValidationService.validateEmail(email);
        
        // Assert
        expect(result.isValid, true);
        expect(result.errorMessage, null);
      });
      
      test('should return invalid result for incorrect email format', () {
        // Arrange
        const email = 'invalid-email';
        
        // Act
        final result = ValidationService.validateEmail(email);
        
        // Assert
        expect(result.isValid, false);
        expect(result.errorMessage, 'Formato de email inválido');
      });
      
      test('should handle empty email', () {
        // Arrange
        const email = '';
        
        // Act
        final result = ValidationService.validateEmail(email);
        
        // Assert
        expect(result.isValid, false);
        expect(result.errorMessage, 'Email es requerido');
      });
      
      test('should handle null email', () {
        // Arrange & Act & Assert
        expect(
          () => ValidationService.validateEmail(null),
          throwsA(isA<ValidationException>()),
        );
      });
    });
    
    group('validateRequired', () {
      test('should return valid for non-empty string', () {
        // Arrange
        const value = 'test value';
        const fieldName = 'Test Field';
        
        // Act
        final result = ValidationService.validateRequired(value, fieldName);
        
        // Assert
        expect(result.isValid, true);
      });
      
      test('should return invalid for empty string', () {
        // Arrange
        const value = '';
        const fieldName = 'Test Field';
        
        // Act
        final result = ValidationService.validateRequired(value, fieldName);
        
        // Assert
        expect(result.isValid, false);
        expect(result.errorMessage, 'Test Field es requerido');
      });
    });
    
    group('sanitizeText', () {
      test('should trim whitespace', () {
        // Arrange
        const input = '  test value  ';
        
        // Act
        final result = ValidationService.sanitizeText(input);
        
        // Assert
        expect(result, 'test value');
      });
      
      test('should remove dangerous characters', () {
        // Arrange
        const input = 'test<script>alert("xss")</script>';
        
        // Act
        final result = ValidationService.sanitizeText(input);
        
        // Assert
        expect(result, contains('test'));
        expect(result, isNot(contains('<script>')));
      });
    });
  });
}
```

### Ejemplo: Testing de Modelos

```dart
// test/models/user_model_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:recount_pro/models/user_model.dart';
import 'package:recount_pro/core/exceptions/app_exceptions.dart';

void main() {
  group('UserModel', () {
    group('constructor', () {
      test('should create user with valid data', () {
        // Arrange & Act
        const user = UserModel(
          uid: 'test-uid',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        // Assert
        expect(user.uid, 'test-uid');
        expect(user.nombre, 'Test User');
        expect(user.correo, '<EMAIL>');
        expect(user.rol, 'verificador');
        expect(user.activo, true); // Default value
      });
    });
    
    group('create factory', () {
      test('should create user with validation', () {
        // Arrange & Act
        final user = UserModel.create(
          uid: 'test-uid',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'VERIFICADOR',
        );
        
        // Assert
        expect(user.correo, '<EMAIL>'); // Should be lowercase
        expect(user.rol, 'verificador'); // Should be lowercase
      });
      
      test('should throw exception for invalid email', () {
        // Arrange & Act & Assert
        expect(
          () => UserModel.create(
            uid: 'test-uid',
            nombre: 'Test User',
            correo: 'invalid-email',
            rol: 'verificador',
          ),
          throwsA(isA<ValidationException>()),
        );
      });
      
      test('should throw exception for invalid role', () {
        // Arrange & Act & Assert
        expect(
          () => UserModel.create(
            uid: 'test-uid',
            nombre: 'Test User',
            correo: '<EMAIL>',
            rol: 'invalid-role',
          ),
          throwsA(isA<ValidationException>()),
        );
      });
    });
    
    group('serialization', () {
      test('should convert to map correctly', () {
        // Arrange
        final user = UserModel.create(
          uid: 'test-uid',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        // Act
        final map = user.toMap();
        
        // Assert
        expect(map['uid'], 'test-uid');
        expect(map['nombre'], 'Test User');
        expect(map['correo'], '<EMAIL>');
        expect(map['rol'], 'verificador');
      });
      
      test('should create from map correctly', () {
        // Arrange
        final map = {
          'uid': 'test-uid',
          'nombre': 'Test User',
          'correo': '<EMAIL>',
          'rol': 'verificador',
          'activo': true,
        };
        
        // Act
        final user = UserModel.fromMap(map);
        
        // Assert
        expect(user.uid, 'test-uid');
        expect(user.nombre, 'Test User');
        expect(user.correo, '<EMAIL>');
        expect(user.rol, 'verificador');
        expect(user.activo, true);
      });
    });
    
    group('utility methods', () {
      test('isAdmin should return true for admin role', () {
        // Arrange
        const user = UserModel(
          uid: 'test-uid',
          nombre: 'Admin User',
          correo: '<EMAIL>',
          rol: 'admin',
        );
        
        // Act & Assert
        expect(user.isAdmin, true);
      });
      
      test('isSupervisorOrAdmin should return true for supervisor', () {
        // Arrange
        const user = UserModel(
          uid: 'test-uid',
          nombre: 'Supervisor User',
          correo: '<EMAIL>',
          rol: 'supervisor',
        );
        
        // Act & Assert
        expect(user.isSupervisorOrAdmin, true);
      });
      
      test('displayName should return nombre when available', () {
        // Arrange
        const user = UserModel(
          uid: 'test-uid',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        // Act & Assert
        expect(user.displayName, 'Test User');
      });
      
      test('displayName should return email prefix when nombre is empty', () {
        // Arrange
        const user = UserModel(
          uid: 'test-uid',
          nombre: '',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        // Act & Assert
        expect(user.displayName, 'test');
      });
    });
    
    group('equatable', () {
      test('should be equal when all properties are same', () {
        // Arrange
        const user1 = UserModel(
          uid: 'test-uid',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        const user2 = UserModel(
          uid: 'test-uid',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        // Act & Assert
        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });
      
      test('should not be equal when properties differ', () {
        // Arrange
        const user1 = UserModel(
          uid: 'test-uid-1',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        const user2 = UserModel(
          uid: 'test-uid-2',
          nombre: 'Test User',
          correo: '<EMAIL>',
          rol: 'verificador',
        );
        
        // Act & Assert
        expect(user1, isNot(equals(user2)));
      });
    });
  });
}
```

## 🎨 Tests de Widgets

### Ejemplo: Testing de Pantallas

```dart
// test/features/auth/login_screen_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:recount_pro/features/auth/login_screen.dart';
import 'package:recount_pro/services/auth_service.dart';

import '../../test_helpers/mock_services.mocks.dart';

void main() {
  group('LoginScreen', () {
    late MockAuthService mockAuthService;
    
    setUp(() {
      mockAuthService = MockAuthService();
    });
    
    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthService>.value(
          value: mockAuthService,
          child: const LoginScreen(),
        ),
      );
    }
    
    group('UI Elements', () {
      testWidgets('should display all required form elements', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert
        expect(find.text('Iniciar Sesión'), findsOneWidget);
        expect(find.byType(TextFormField), findsNWidgets(2));
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Contraseña'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
        expect(find.text('¿Olvidaste tu contraseña?'), findsOneWidget);
      });
      
      testWidgets('should show password visibility toggle', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert
        expect(find.byIcon(Icons.visibility_outlined), findsOneWidget);
      });
    });
    
    group('Form Validation', () {
      testWidgets('should show validation errors for empty fields', (tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        
        // Act
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        // Assert
        expect(find.text('Email es requerido'), findsOneWidget);
        expect(find.text('Contraseña es requerida'), findsOneWidget);
      });
      
      testWidgets('should show error for invalid email format', (tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        
        // Act
        await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        // Assert
        expect(find.text('Formato de email inválido'), findsOneWidget);
      });
    });
    
    group('Authentication', () {
      testWidgets('should call signIn when form is valid', (tester) async {
        // Arrange
        when(mockAuthService.signInWithEmailAndPassword(any, any))
            .thenAnswer((_) async => MockUserCredential());
        
        await tester.pumpWidget(createTestWidget());
        
        // Act
        await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
        await tester.enterText(find.byType(TextFormField).last, 'password123');
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        // Assert
        verify(mockAuthService.signInWithEmailAndPassword('<EMAIL>', 'password123'));
      });
      
      testWidgets('should show loading indicator during authentication', (tester) async {
        // Arrange
        when(mockAuthService.signInWithEmailAndPassword(any, any))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(seconds: 1));
          return MockUserCredential();
        });
        
        await tester.pumpWidget(createTestWidget());
        
        // Act
        await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
        await tester.enterText(find.byType(TextFormField).last, 'password123');
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
      
      testWidgets('should show error message on authentication failure', (tester) async {
        // Arrange
        when(mockAuthService.signInWithEmailAndPassword(any, any))
            .thenThrow(Exception('Invalid credentials'));
        
        await tester.pumpWidget(createTestWidget());
        
        // Act
        await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
        await tester.enterText(find.byType(TextFormField).last, 'wrongpassword');
        await tester.tap(find.byType(ElevatedButton));
        await tester.pumpAndSettle();
        
        // Assert
        expect(find.text('Error de autenticación'), findsOneWidget);
      });
    });
    
    group('Password Visibility', () {
      testWidgets('should toggle password visibility', (tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        
        // Act - Tap visibility toggle
        await tester.tap(find.byIcon(Icons.visibility_outlined));
        await tester.pump();
        
        // Assert - Icon should change
        expect(find.byIcon(Icons.visibility_off_outlined), findsOneWidget);
        
        // Act - Tap again
        await tester.tap(find.byIcon(Icons.visibility_off_outlined));
        await tester.pump();
        
        // Assert - Icon should change back
        expect(find.byIcon(Icons.visibility_outlined), findsOneWidget);
      });
    });
  });
}
```

## 🔗 Tests de Integración

### Ejemplo: Flujo Completo de Login

```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:recount_pro/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('ReCount Pro Integration Tests', () {
    testWidgets('complete login flow', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      // Act - Navigate to login screen
      await tester.tap(find.text('Iniciar Sesión'));
      await tester.pumpAndSettle();
      
      // Assert - Should be on login screen
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Contraseña'), findsOneWidget);
      
      // Act - Enter valid credentials
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Assert - Should navigate to main screen
      expect(find.text('Dashboard'), findsOneWidget);
    });
    
    testWidgets('navigation between screens', (tester) async {
      // Arrange - Assume user is logged in
      app.main();
      await tester.pumpAndSettle();
      
      // Act - Navigate to profile
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      
      // Assert
      expect(find.text('Perfil'), findsOneWidget);
      
      // Act - Navigate to settings
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();
      
      // Assert
      expect(find.text('Configuración'), findsOneWidget);
    });
  });
}
```

## 🎭 Mocks y Stubs

### Configuración de Mocks

```dart
// test/test_helpers/mock_services.dart
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:recount_pro/services/auth_service.dart';
import 'package:recount_pro/services/firebase_service.dart';

@GenerateMocks([
  AuthService,
  FirebaseService,
  User,
  UserCredential,
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  QuerySnapshot,
])
void main() {}
```

### Datos de Prueba

```dart
// test/test_helpers/test_data.dart
import 'package:recount_pro/models/user_model.dart';
import 'package:recount_pro/models/vh_model.dart';

class TestData {
  static const UserModel testUser = UserModel(
    uid: 'test-uid-123',
    nombre: 'Test User',
    correo: '<EMAIL>',
    rol: 'verificador',
  );
  
  static const UserModel adminUser = UserModel(
    uid: 'admin-uid-123',
    nombre: 'Admin User',
    correo: '<EMAIL>',
    rol: 'admin',
  );
  
  static final VhProgramado testVh = VhProgramado(
    vhId: 'VH-001',
    placa: 'ABC-123',
    fecha: DateTime(2024, 1, 15),
    productos: [
      const ProductoVh(
        sku: 'SKU-001',
        descripcion: 'Producto Test 1',
        cantidadProgramada: 10,
      ),
      const ProductoVh(
        sku: 'SKU-002',
        descripcion: 'Producto Test 2',
        cantidadProgramada: 5,
      ),
    ],
  );
  
  static Map<String, dynamic> get testUserMap => {
    'uid': testUser.uid,
    'nombre': testUser.nombre,
    'correo': testUser.correo,
    'rol': testUser.rol,
    'activo': testUser.activo,
  };
}
```

### Utilidades de Testing

```dart
// test/test_helpers/test_utils.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:recount_pro/services/auth_service.dart';
import 'mock_services.mocks.dart';

class TestUtils {
  /// Crea un widget de prueba con providers mockeados
  static Widget createTestWidget({
    required Widget child,
    MockAuthService? authService,
  }) {
    return MaterialApp(
      home: MultiProvider(
        providers: [
          ChangeNotifierProvider<AuthService>.value(
            value: authService ?? MockAuthService(),
          ),
        ],
        child: child,
      ),
    );
  }
  
  /// Espera hasta que aparezca un widget específico
  static Future<void> waitForWidget(
    WidgetTester tester,
    Finder finder, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final end = DateTime.now().add(timeout);
    
    while (DateTime.now().isBefore(end)) {
      await tester.pump(const Duration(milliseconds: 100));
      
      if (finder.evaluate().isNotEmpty) {
        return;
      }
    }
    
    throw TimeoutException('Widget not found within timeout', timeout);
  }
  
  /// Simula entrada de texto en un campo específico
  static Future<void> enterTextInField(
    WidgetTester tester,
    String text,
    String labelText,
  ) async {
    final field = find.widgetWithText(TextFormField, labelText);
    await tester.enterText(field, text);
    await tester.pump();
  }
}

class TimeoutException implements Exception {
  final String message;
  final Duration timeout;
  
  const TimeoutException(this.message, this.timeout);
  
  @override
  String toString() => 'TimeoutException: $message (${timeout.inSeconds}s)';
}
```

## 📊 Cobertura de Código

### Generar Reporte de Cobertura

```bash
# Ejecutar tests con cobertura
flutter test --coverage

# Generar reporte HTML
genhtml coverage/lcov.info -o coverage/html

# Abrir reporte
open coverage/html/index.html
```

### Configuración de Cobertura

```yaml
# test/coverage_helper_test.dart
// Helper file to import all files for coverage
import 'package:recount_pro/main.dart';
import 'package:recount_pro/core/services/analytics_service.dart';
import 'package:recount_pro/core/services/cache_service.dart';
// ... importar todos los archivos que queremos en cobertura

void main() {
  // Este archivo no contiene tests reales
  // Solo importa archivos para incluirlos en cobertura
}
```

### Métricas de Cobertura Objetivo

| Componente | Cobertura Mínima | Cobertura Actual |
|------------|------------------|------------------|
| Servicios Core | 95% | 98% |
| Modelos | 90% | 95% |
| Repositorios | 85% | 88% |
| Widgets | 70% | 75% |
| Screens | 60% | 65% |
| **Total** | **80%** | **85%** |

## 🚀 CI/CD Testing

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5'
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Generate mocks
      run: flutter packages pub run build_runner build
      
    - name: Run analyzer
      run: flutter analyze
      
    - name: Run unit tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        
    - name: Run integration tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: flutter test integration_test/
```

### Script de Testing Local

```bash
#!/bin/bash
# scripts/run_tests.sh

echo "🧪 Ejecutando suite completa de tests..."

# Limpiar proyecto
echo "🧹 Limpiando proyecto..."
flutter clean
flutter pub get

# Generar mocks
echo "🎭 Generando mocks..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Análisis estático
echo "🔍 Ejecutando análisis estático..."
flutter analyze
if [ $? -ne 0 ]; then
    echo "❌ Análisis estático falló"
    exit 1
fi

# Tests unitarios
echo "🧪 Ejecutando tests unitarios..."
flutter test --coverage
if [ $? -ne 0 ]; then
    echo "❌ Tests unitarios fallaron"
    exit 1
fi

# Tests de integración
echo "🔗 Ejecutando tests de integración..."
flutter test integration_test/
if [ $? -ne 0 ]; then
    echo "❌ Tests de integración fallaron"
    exit 1
fi

# Generar reporte de cobertura
echo "📊 Generando reporte de cobertura..."
genhtml coverage/lcov.info -o coverage/html

echo "✅ Todos los tests pasaron exitosamente!"
echo "📊 Reporte de cobertura disponible en: coverage/html/index.html"
```

### Configuración de Pre-commit Hooks

```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "Ejecutando pre-commit hooks..."

# Formatear código
dart format .

# Ejecutar análisis
flutter analyze
if [ $? -ne 0 ]; then
    echo "❌ Análisis estático falló. Commit cancelado."
    exit 1
fi

# Ejecutar tests unitarios
flutter test
if [ $? -ne 0 ]; then
    echo "❌ Tests unitarios fallaron. Commit cancelado."
    exit 1
fi

echo "✅ Pre-commit hooks pasaron exitosamente!"
```
