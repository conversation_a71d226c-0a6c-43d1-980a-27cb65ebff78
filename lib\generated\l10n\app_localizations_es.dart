// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'ReCount Pro';

  @override
  String get welcome => 'Bienvenido';

  @override
  String get login => 'Iniciar Sesión';

  @override
  String get logout => 'Cerrar Sesión';

  @override
  String get email => 'Correo Electrónico';

  @override
  String get password => 'Contraseña';

  @override
  String get forgotPassword => '¿Olvidaste tu contraseña?';

  @override
  String get signIn => 'Ingresar';

  @override
  String get signUp => 'Registrarse';

  @override
  String get profile => 'Perfil';

  @override
  String get settings => 'Configuración';

  @override
  String get theme => 'Tema';

  @override
  String get lightTheme => 'Claro';

  @override
  String get darkTheme => 'Oscuro';

  @override
  String get systemTheme => 'Sistema';

  @override
  String get language => 'Idioma';

  @override
  String get spanish => 'Español';

  @override
  String get english => 'Inglés';

  @override
  String get conteo => 'Conteo';

  @override
  String get vhId => 'ID VH';

  @override
  String get placa => 'Placa';

  @override
  String get productos => 'Productos';

  @override
  String get sku => 'SKU';

  @override
  String get descripcion => 'Descripción';

  @override
  String get cantidad => 'Cantidad';

  @override
  String get programado => 'Programado';

  @override
  String get fisico => 'Físico';

  @override
  String get diferencia => 'Diferencia';

  @override
  String get novedades => 'Novedades';

  @override
  String get faltante => 'Faltante';

  @override
  String get sobrante => 'Sobrante';

  @override
  String get guardar => 'Guardar';

  @override
  String get cancelar => 'Cancelar';

  @override
  String get confirmar => 'Confirmar';

  @override
  String get buscar => 'Buscar';

  @override
  String get filtrar => 'Filtrar';

  @override
  String get exportar => 'Exportar';

  @override
  String get imprimir => 'Imprimir';

  @override
  String get error => 'Error';

  @override
  String get success => 'Éxito';

  @override
  String get warning => 'Advertencia';

  @override
  String get info => 'Información';

  @override
  String get loading => 'Cargando...';

  @override
  String get retry => 'Reintentar';

  @override
  String get close => 'Cerrar';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get ok => 'Aceptar';

  @override
  String get general => 'General';

  @override
  String get metrics => 'Métricas';

  @override
  String get analytics => 'Analíticas';

  @override
  String get sessionDuration => 'Duración de Sesión';

  @override
  String get totalSessions => 'Total de Sesiones';

  @override
  String get productivity => 'Productividad';

  @override
  String get errorRate => 'Tasa de Error';

  @override
  String get version => 'Versión';

  @override
  String get checkUpdates => 'Buscar Actualizaciones';

  @override
  String get clearCache => 'Limpiar Caché';

  @override
  String get autoSync => 'Sincronización Automática';

  @override
  String get offlineMode => 'Modo Sin Conexión';

  @override
  String get exportMetrics => 'Exportar Métricas';

  @override
  String get resetMetrics => 'Reiniciar Métricas';

  @override
  String get collectMetrics => 'Recopilar Métricas';

  @override
  String get errorReporting => 'Reportes de Errores';
}
