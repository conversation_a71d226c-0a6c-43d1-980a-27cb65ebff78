import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../models/pattern_analysis_model.dart';
import '../../models/ml_models.dart';
import '../utils/logger.dart';

/// Servicio avanzado de Machine Learning - FASE 2
/// Implementa algoritmos sofisticados como Random Forest y Neural Networks
class AdvancedMLService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Modelos entrenados
  RandomForestModel? _novedadModel;
  NeuralNetworkModel? _tipoNovedadModel;
  TimeSeriesModel? _demandaModel;

  // Cache de features y predicciones
  final Map<String, List<double>> _featureCache = {};
  final Map<String, AdvancedPrediction> _predictionCache = {};

  // Métricas del modelo
  ModelMetrics _modelMetrics = ModelMetrics.empty();

  // Estado del servicio
  bool _isInitialized = false;
  bool _isTraining = false;
  DateTime? _lastTraining;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isTraining => _isTraining;
  ModelMetrics get modelMetrics => _modelMetrics;
  DateTime? get lastTraining => _lastTraining;

  /// Inicializar el servicio de ML avanzado
  Future<void> initialize() async {
    try {
      Logger.info('🤖 [ADVANCED_ML] Inicializando servicio de ML avanzado...');

      // Cargar modelos pre-entrenados si existen
      await _loadPretrainedModels();

      // Si no hay modelos, entrenar con datos existentes
      if (_novedadModel == null) {
        await trainModels();
      }

      _isInitialized = true;
      Logger.info('✅ [ADVANCED_ML] Servicio inicializado correctamente');
      notifyListeners();

    } catch (e, stackTrace) {
      Logger.error('❌ [ADVANCED_ML] Error inicializando servicio', e, stackTrace);
    }
  }

  /// Entrenar todos los modelos con datos históricos
  Future<void> trainModels() async {
    if (_isTraining) {
      Logger.warning('⚠️ [ADVANCED_ML] Entrenamiento ya en progreso');
      return;
    }

    try {
      _isTraining = true;
      notifyListeners();

      Logger.info('🎓 [ADVANCED_ML] Iniciando entrenamiento de modelos...');

      // Cargar datos de entrenamiento
      final trainingData = await _loadTrainingData();
      Logger.info('📊 [ADVANCED_ML] Datos de entrenamiento cargados: ${trainingData.length} registros');

      if (trainingData.length < 50) {
        Logger.warning('⚠️ [ADVANCED_ML] Pocos datos para entrenamiento (${trainingData.length}). Generando datos sintéticos...');
        await _generateSyntheticData();
        final syntheticData = await _loadTrainingData();
        trainingData.addAll(syntheticData);
      }

      // Preparar features
      final features = _extractFeatures(trainingData);
      final labels = _extractLabels(trainingData);

      // Entrenar modelo de predicción de novedades (Random Forest)
      _novedadModel = await _trainRandomForestModel(features, labels['novedad']!);
      Logger.info('✅ [ADVANCED_ML] Modelo Random Forest entrenado');

      // Entrenar modelo de tipo de novedad (Neural Network)
      _tipoNovedadModel = await _trainNeuralNetworkModel(features, labels['tipo']!);
      Logger.info('✅ [ADVANCED_ML] Modelo Neural Network entrenado');

      // Entrenar modelo de series temporales
      _demandaModel = await _trainTimeSeriesModel(trainingData);
      Logger.info('✅ [ADVANCED_ML] Modelo Time Series entrenado');

      // Evaluar modelos
      await _evaluateModels(features, labels);

      // Guardar modelos entrenados
      await _saveTrainedModels();

      _lastTraining = DateTime.now();
      _isTraining = false;

      Logger.info('🎉 [ADVANCED_ML] Entrenamiento completado exitosamente');
      notifyListeners();

    } catch (e, stackTrace) {
      _isTraining = false;
      Logger.error('❌ [ADVANCED_ML] Error durante entrenamiento', e, stackTrace);
      notifyListeners();
    }
  }

  /// Hacer predicción avanzada para un VH específico
  Future<AdvancedPrediction> predictAdvanced({
    required String vhId,
    required String verificadorId,
    required DateTime fechaSalida,
    String? ruta,
    List<String>? skus,
    Map<String, dynamic>? contextData,
  }) async {
    try {
      // Verificar si está en cache
      final cacheKey = '$vhId-$verificadorId-${fechaSalida.millisecondsSinceEpoch}';
      if (_predictionCache.containsKey(cacheKey)) {
        return _predictionCache[cacheKey]!;
      }

      // Extraer features para predicción
      final features = await _extractPredictionFeatures(
        vhId: vhId,
        verificadorId: verificadorId,
        fechaSalida: fechaSalida,
        ruta: ruta,
        skus: skus,
        contextData: contextData,
      );

      // Predicción con Random Forest (probabilidad de novedad)
      final novedadProbability = _novedadModel?.predict(features) ?? 0.1;

      // Predicción con Neural Network (tipo de novedad)
      final tipoProbs = _tipoNovedadModel?.predictMulticlass(features) ?? {'Faltante': 0.6, 'Sobrante': 0.4};
      final tipoMasProbable = tipoProbs.entries.reduce((a, b) => a.value > b.value ? a : b).key;

      // Predicción de demanda con Time Series
      final demandaPrediction = await _predictDemanda(fechaSalida, ruta);

      // Generar recomendaciones inteligentes
      final recommendations = await _generateSmartRecommendations(
        features: features,
        novedadProb: novedadProbability,
        tipoNovedad: tipoMasProbable,
        demanda: demandaPrediction,
        contextData: contextData,
      );

      // Calcular confianza del modelo
      final confidence = _calculateModelConfidence(features, novedadProbability);

      final prediction = AdvancedPrediction(
        vhId: vhId,
        verificadorId: verificadorId,
        fechaPrediccion: DateTime.now(),
        probabilidadNovedad: novedadProbability,
        tipoNovedadMasProbable: tipoMasProbable,
        probabilidadesTipo: tipoProbs,
        confianza: confidence,
        recomendaciones: recommendations,
        factoresInfluencia: _analyzeInfluenceFactors(features),
        demandaPrediccion: demandaPrediction,
        riesgoOperacional: _calculateOperationalRisk(novedadProbability, demandaPrediction),
        accionesRecomendadas: _generateActionItems(novedadProbability, recommendations),
      );

      // Guardar en cache
      _predictionCache[cacheKey] = prediction;

      return prediction;

    } catch (e, stackTrace) {
      Logger.error('❌ [ADVANCED_ML] Error en predicción avanzada', e, stackTrace);

      // Predicción por defecto
      return AdvancedPrediction.defaultPrediction(vhId, verificadorId);
    }
  }

  /// Cargar datos de entrenamiento desde Firebase
  Future<List<PatternAnalysisData>> _loadTrainingData() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 180)); // 6 meses

      final querySnapshot = await _firestore
          .collection('segundos_conteos')
          .where('fecha', isGreaterThan: Timestamp.fromDate(cutoffDate))
          .orderBy('fecha', descending: true)
          .limit(2000)
          .get();

      return querySnapshot.docs
          .map((doc) => PatternAnalysisData.fromSegundoConteo({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();

    } catch (e, stackTrace) {
      Logger.error('❌ [ADVANCED_ML] Error cargando datos de entrenamiento', e, stackTrace);
      return [];
    }
  }

  /// Extraer features para machine learning
  Map<String, List<List<double>>> _extractFeatures(List<PatternAnalysisData> data) {
    final features = <List<double>>[];

    for (final item in data) {
      final feature = [
        // Features temporales
        item.diaSemana.toDouble(),
        item.hora.toDouble(),
        item.fechaConteo.month.toDouble(),

        // Features del VH
        item.cantidadSkus.toDouble(),
        item.pesoTotal,

        // Features del verificador (hash para anonimizar)
        item.verificadorId.hashCode.abs() % 1000 / 1000.0,

        // Features de contexto
        (item.ruta?.hashCode.abs() ?? 0) % 100 / 100.0,
        item.tiempoConteoMinutos.toDouble(),

        // Features derivadas
        _calculateWorkloadFactor(item),
        _calculateSeasonalityFactor(item.fechaConteo),
        _calculateVerificadorExperience(item.verificadorId),
      ];

      features.add(feature);
    }

    return {'features': features};
  }

  /// Extraer labels para entrenamiento
  Map<String, List<double>> _extractLabels(List<PatternAnalysisData> data) {
    final novedadLabels = <double>[];
    final tipoLabels = <double>[];

    for (final item in data) {
      // Label para predicción de novedad (0 = no, 1 = sí)
      novedadLabels.add(item.tieneNovedad ? 1.0 : 0.0);

      // Label para tipo de novedad (0 = faltante, 1 = sobrante)
      if (item.tieneNovedad) {
        tipoLabels.add(item.tipoNovedad == 'Sobrante' ? 1.0 : 0.0);
      } else {
        tipoLabels.add(0.0); // Default a faltante si no hay novedad
      }
    }

    return {
      'novedad': novedadLabels,
      'tipo': tipoLabels,
    };
  }

  /// Entrenar modelo Random Forest
  Future<RandomForestModel> _trainRandomForestModel(
    Map<String, List<List<double>>> features,
    List<double> labels,
  ) async {
    // Implementación simplificada de Random Forest
    // En producción, se usaría una librería como TensorFlow Lite

    final model = RandomForestModel(
      numTrees: 10,
      maxDepth: 5,
      minSamplesLeaf: 2,
    );

    await model.train(features['features']!, labels);

    return model;
  }

  /// Entrenar modelo Neural Network
  Future<NeuralNetworkModel> _trainNeuralNetworkModel(
    Map<String, List<List<double>>> features,
    List<double> labels,
  ) async {
    // Implementación simplificada de Neural Network
    // En producción, se usaría TensorFlow Lite o similar

    final model = NeuralNetworkModel(
      inputSize: features['features']!.first.length,
      hiddenLayers: [16, 8],
      outputSize: 2, // Faltante o Sobrante
      learningRate: 0.01,
    );

    await model.train(features['features']!, labels);

    return model;
  }

  /// Entrenar modelo de series temporales
  Future<TimeSeriesModel> _trainTimeSeriesModel(List<PatternAnalysisData> data) async {
    // Agrupar datos por día para análisis temporal
    final dailyData = <DateTime, int>{};

    for (final item in data) {
      final day = DateTime(item.fechaConteo.year, item.fechaConteo.month, item.fechaConteo.day);
      dailyData[day] = (dailyData[day] ?? 0) + 1;
    }

    final model = TimeSeriesModel();
    await model.train(dailyData);

    return model;
  }

  // Métodos auxiliares
  double _calculateWorkloadFactor(PatternAnalysisData data) {
    // Factor basado en carga de trabajo
    return (data.cantidadSkus * data.pesoTotal) / 1000.0;
  }

  double _calculateSeasonalityFactor(DateTime date) {
    // Factor estacional basado en mes y día
    final dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays;
    return sin(2 * pi * dayOfYear / 365.0);
  }

  double _calculateVerificadorExperience(String verificadorId) {
    // Simulación de experiencia del verificador
    return (verificadorId.hashCode.abs() % 100) / 100.0;
  }

  /// Cargar modelos pre-entrenados
  Future<void> _loadPretrainedModels() async {
    try {
      // En una implementación real, cargaríamos desde Firebase Storage
      // Por ahora, simulamos que no hay modelos pre-entrenados
      Logger.info('📥 [ADVANCED_ML] Buscando modelos pre-entrenados...');
    } catch (e) {
      Logger.info('ℹ️ [ADVANCED_ML] No se encontraron modelos pre-entrenados');
    }
  }

  /// Guardar modelos entrenados
  Future<void> _saveTrainedModels() async {
    try {
      // En una implementación real, guardaríamos en Firebase Storage
      Logger.info('💾 [ADVANCED_ML] Guardando modelos entrenados...');
    } catch (e, stackTrace) {
      Logger.error('❌ [ADVANCED_ML] Error guardando modelos', e, stackTrace);
    }
  }

  /// Generar datos sintéticos para entrenamiento
  Future<void> _generateSyntheticData() async {
    try {
      Logger.info('🧪 [ADVANCED_ML] Generando datos sintéticos...');

      final batch = _firestore.batch();
      final random = Random();
      final now = DateTime.now();

      for (int i = 0; i < 200; i++) {
        final fecha = now.subtract(Duration(days: random.nextInt(90)));

        // Generar datos con patrones conocidos
        bool tieneNovedad = false;

        // Patrón: viernes más problemáticos
        if (fecha.weekday == 5) {
          tieneNovedad = random.nextDouble() < 0.6;
        }
        // Patrón: horas pico
        else if (fecha.hour >= 14 && fecha.hour <= 16) {
          tieneNovedad = random.nextDouble() < 0.5;
        }
        else {
          tieneNovedad = random.nextDouble() < 0.2;
        }

        final data = PatternAnalysisData(
          id: 'synthetic_${DateTime.now().millisecondsSinceEpoch}_$i',
          vhId: 'VH_${random.nextInt(100)}',
          placa: 'SYN${random.nextInt(999)}',
          verificadorId: 'verificador_${random.nextInt(5)}',
          conductorId: 'conductor_${random.nextInt(10)}',
          ruta: ['Norte', 'Sur', 'Este', 'Oeste'][random.nextInt(4)],
          fechaConteo: fecha,
          diaSemana: fecha.weekday,
          hora: fecha.hour,
          cantidadSkus: random.nextInt(20) + 5,
          pesoTotal: random.nextDouble() * 500 + 100,
          tieneNovedad: tieneNovedad,
          tipoNovedad: tieneNovedad ? (random.nextBool() ? 'Faltante' : 'Sobrante') : null,
          skusConNovedad: tieneNovedad ? ['SKU_${random.nextInt(10)}'] : [],
          tiempoConteoMinutos: random.nextInt(30) + 10,
          metadatos: const {
            'synthetic': true,
            'pattern': 'generated',
          },
        );

        final docRef = _firestore.collection('segundos_conteos').doc();
        batch.set(docRef, data.toMap());
      }

      await batch.commit();
      Logger.info('✅ [ADVANCED_ML] 200 registros sintéticos generados');

    } catch (e, stackTrace) {
      Logger.error('❌ [ADVANCED_ML] Error generando datos sintéticos', e, stackTrace);
    }
  }

  /// Extraer features para predicción específica
  Future<List<double>> _extractPredictionFeatures({
    required String vhId,
    required String verificadorId,
    required DateTime fechaSalida,
    String? ruta,
    List<String>? skus,
    Map<String, dynamic>? contextData,
  }) async {
    return [
      // Features temporales
      fechaSalida.weekday.toDouble(),
      fechaSalida.hour.toDouble(),
      fechaSalida.month.toDouble(),

      // Features del VH
      (skus?.length ?? 10).toDouble(),
      (contextData?['pesoEstimado'] ?? 200.0).toDouble(),

      // Features del verificador
      verificadorId.hashCode.abs() % 1000 / 1000.0,

      // Features de contexto
      (ruta?.hashCode.abs() ?? 0) % 100 / 100.0,
      (contextData?['tiempoEstimado'] ?? 20.0).toDouble(),

      // Features derivadas
      _calculateWorkloadFactor(PatternAnalysisData(
        id: 'temp',
        vhId: vhId,
        placa: 'temp',
        verificadorId: verificadorId,
        fechaConteo: fechaSalida,
        diaSemana: fechaSalida.weekday,
        hora: fechaSalida.hour,
        cantidadSkus: skus?.length ?? 10,
        pesoTotal: (contextData?['pesoEstimado'] ?? 200.0).toDouble(),
        tieneNovedad: false,
        skusConNovedad: const [],
        tiempoConteoMinutos: (contextData?['tiempoEstimado'] ?? 20.0).toInt(),
        metadatos: const {},
      )),
      _calculateSeasonalityFactor(fechaSalida),
      _calculateVerificadorExperience(verificadorId),
    ];
  }

  /// Predicción de demanda
  Future<DemandaPrediction> _predictDemanda(DateTime fecha, String? ruta) async {
    if (_demandaModel == null) {
      return DemandaPrediction.empty();
    }

    return _demandaModel!.predict(fecha, ruta);
  }

  /// Generar recomendaciones inteligentes
  Future<List<String>> _generateSmartRecommendations({
    required List<double> features,
    required double novedadProb,
    required String tipoNovedad,
    required DemandaPrediction demanda,
    Map<String, dynamic>? contextData,
  }) async {
    final recommendations = <String>[];

    // Recomendaciones basadas en probabilidad de novedad
    if (novedadProb > 0.7) {
      recommendations.add('🚨 Alta probabilidad de novedad - Verificación doble recomendada');
      recommendations.add('📋 Revisar historial de este VH antes del conteo');
    } else if (novedadProb > 0.4) {
      recommendations.add('⚠️ Probabilidad moderada de novedad - Atención especial');
    }

    // Recomendaciones basadas en tipo de novedad
    if (tipoNovedad == 'Faltante') {
      recommendations.add('📦 Revisar proceso de alistamiento previo');
      recommendations.add('🔍 Verificar completitud de carga');
    } else {
      recommendations.add('📊 Revisar proceso de conteo inicial');
      recommendations.add('⚖️ Verificar pesaje de productos');
    }

    // Recomendaciones basadas en demanda
    if (demanda.demandaEsperada > 40) {
      recommendations.add('📈 Alta demanda esperada - Asignar verificador experimentado');
    }

    if (demanda.variabilidad > 15) {
      recommendations.add('📊 Alta variabilidad - Preparar para múltiples escenarios');
    }

    // Recomendaciones temporales
    final hora = features[1].toInt();
    if (hora >= 14 && hora <= 16) {
      recommendations.add('⏰ Hora pico detectada - Considerar descanso previo');
    }

    // Recomendaciones por día de la semana
    final diaSemana = features[0].toInt();
    if (diaSemana == 5) { // Viernes
      recommendations.add('📅 Viernes detectado - Día con mayor incidencia de novedades');
    }

    return recommendations.take(4).toList(); // Máximo 4 recomendaciones
  }

  /// Calcular confianza del modelo
  double _calculateModelConfidence(List<double> features, double prediction) {
    // Confianza basada en la distancia a los datos de entrenamiento
    // y la consistencia de la predicción

    double confidence = 0.5; // Base

    // Factor 1: Cantidad de datos de entrenamiento
    if (_modelMetrics.totalPredictions > 100) {
      confidence += 0.2;
    } else if (_modelMetrics.totalPredictions > 50) {
      confidence += 0.1;
    }

    // Factor 2: Accuracy del modelo
    confidence += _modelMetrics.accuracy * 0.3;

    // Factor 3: Estabilidad de la predicción
    if (prediction > 0.1 && prediction < 0.9) {
      confidence += 0.1; // Predicciones no extremas son más confiables
    }

    return min(1.0, confidence);
  }

  /// Analizar factores de influencia
  Map<String, dynamic> _analyzeInfluenceFactors(List<double> features) {
    return {
      'dia_semana': {
        'valor': features[0],
        'impacto': _calculateFeatureImpact(0, features[0]),
      },
      'hora': {
        'valor': features[1],
        'impacto': _calculateFeatureImpact(1, features[1]),
      },
      'verificador': {
        'valor': features[5],
        'impacto': _calculateFeatureImpact(5, features[5]),
      },
      'carga_trabajo': {
        'valor': features[9],
        'impacto': _calculateFeatureImpact(9, features[9]),
      },
    };
  }

  /// Calcular impacto de una feature
  double _calculateFeatureImpact(int featureIndex, double value) {
    // Simulación de importancia de features
    switch (featureIndex) {
      case 0: // día de la semana
        return value == 5.0 ? 0.8 : 0.3; // Viernes más impacto
      case 1: // hora
        return (value >= 14 && value <= 16) ? 0.7 : 0.2; // Hora pico
      case 5: // verificador
        return value > 0.7 ? 0.6 : 0.4; // Verificadores experimentados
      default:
        return 0.3;
    }
  }

  /// Calcular riesgo operacional
  double _calculateOperationalRisk(double novedadProb, DemandaPrediction demanda) {
    double risk = novedadProb * 0.6; // Base del riesgo

    // Factor de demanda
    if (demanda.demandaEsperada > 50) {
      risk += 0.2;
    }

    // Factor de variabilidad
    if (demanda.variabilidad > 20) {
      risk += 0.2;
    }

    return min(1.0, risk);
  }

  /// Generar items de acción
  List<ActionItem> _generateActionItems(double novedadProb, List<String> recommendations) {
    final actions = <ActionItem>[];

    if (novedadProb > 0.7) {
      actions.add(ActionItem(
        id: 'high_risk_${DateTime.now().millisecondsSinceEpoch}',
        titulo: 'Verificación Doble Requerida',
        descripcion: 'Alta probabilidad de novedad detectada. Realizar verificación con supervisor.',
        prioridad: 'alta',
        categoria: 'proceso',
        fechaLimite: DateTime.now().add(const Duration(hours: 1)),
        metadatos: {'probabilidad': novedadProb},
      ));
    }

    if (novedadProb > 0.4) {
      actions.add(ActionItem(
        id: 'medium_risk_${DateTime.now().millisecondsSinceEpoch}',
        titulo: 'Atención Especial',
        descripcion: 'Probabilidad moderada de novedad. Revisar con cuidado.',
        prioridad: 'media',
        categoria: 'verificador',
        fechaLimite: DateTime.now().add(const Duration(hours: 2)),
        metadatos: {'probabilidad': novedadProb},
      ));
    }

    return actions;
  }

  /// Evaluar modelos entrenados
  Future<void> _evaluateModels(Map<String, List<List<double>>> features, Map<String, List<double>> labels) async {
    try {
      Logger.info('📊 [ADVANCED_ML] Evaluando modelos...');

      if (features['features']!.isEmpty || labels['novedad']!.isEmpty) {
        Logger.warning('⚠️ [ADVANCED_ML] No hay datos suficientes para evaluación');
        return;
      }

      // Dividir datos en entrenamiento y prueba (80/20)
      final totalSamples = features['features']!.length;
      final trainSize = (totalSamples * 0.8).round();

      final testFeatures = features['features']!.sublist(trainSize);
      final testLabels = labels['novedad']!.sublist(trainSize);

      // Evaluar modelo de novedades
      int correctPredictions = 0;
      int totalPredictions = testFeatures.length;

      for (int i = 0; i < testFeatures.length; i++) {
        final prediction = _novedadModel?.predict(testFeatures[i]) ?? 0.1;
        final predicted = prediction > 0.5 ? 1.0 : 0.0;
        final actual = testLabels[i];

        if (predicted == actual) {
          correctPredictions++;
        }
      }

      final accuracy = totalPredictions > 0 ? correctPredictions / totalPredictions : 0.0;

      _modelMetrics = ModelMetrics(
        accuracy: accuracy,
        precision: accuracy, // Simplificado
        recall: accuracy, // Simplificado
        f1Score: accuracy, // Simplificado
        totalPredictions: totalPredictions,
        correctPredictions: correctPredictions,
        lastEvaluation: DateTime.now(),
        classMetrics: {
          'novedad': accuracy,
          'no_novedad': accuracy,
        },
      );

      Logger.info('✅ [ADVANCED_ML] Evaluación completada - Accuracy: ${(accuracy * 100).toStringAsFixed(1)}%');

    } catch (e, stackTrace) {
      Logger.error('❌ [ADVANCED_ML] Error evaluando modelos', e, stackTrace);
    }
  }

  /// Limpiar cache de predicciones
  void clearPredictionCache() {
    _predictionCache.clear();
    notifyListeners();
  }

  /// Obtener estadísticas del servicio
  Map<String, dynamic> getServiceStats() {
    return {
      'isInitialized': _isInitialized,
      'isTraining': _isTraining,
      'lastTraining': _lastTraining?.toIso8601String(),
      'modelMetrics': {
        'accuracy': _modelMetrics.accuracy,
        'totalPredictions': _modelMetrics.totalPredictions,
        'correctPredictions': _modelMetrics.correctPredictions,
      },
      'cacheSize': _predictionCache.length,
      'modelsLoaded': {
        'randomForest': _novedadModel != null,
        'neuralNetwork': _tipoNovedadModel != null,
        'timeSeries': _demandaModel != null,
      },
    };
  }
}