import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';
import 'core/services/theme_service.dart';
import 'core/services/localization_service.dart';

import 'generated/l10n/app_localizations.dart';
import 'features/splash/splash_screen.dart';
import 'features/auth/login_screen.dart';
import 'features/home/<USER>';
import 'features/profile/profile_screen.dart';
import 'features/conteo/conteo_screen.dart';
import 'features/pdf_generator/pdf_generator_screen.dart';

import 'features/admin/data_admin_screen.dart';
import 'features/admin/excel_preview_screen.dart';
import 'features/admin/data_import_screen.dart';
import 'features/conteo/segundo_conteo_screen_new.dart';
import 'features/data_management/data_management_screen.dart';
import 'features/analytics/pattern_dashboard_screen.dart';
import 'features/analytics/advanced_dashboard_screen.dart';


class ReCountProApp extends StatelessWidget {
  const ReCountProApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Consumer<LocalizationService>(
          builder: (context, localizationService, child) {
        return MaterialApp(
          title: 'ReCount Pro',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeService.themeMode,
          debugShowCheckedModeBanner: false,

          // Localización
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          locale: localizationService.currentLocale,
          localeResolutionCallback: localizationService.localeResolutionCallback,

          // Iniciar con SplashScreen según especificaciones del súper prompt
          home: const SplashScreen(),
          routes: {
            '/splash': (context) => const SplashScreen(),
            '/login': (context) => const LoginScreen(),
            '/home': (context) => const HomeScreen(),
            '/profile': (context) => const ProfileScreen(),
            '/conteo': (context) => const ConteoScreen(),
            '/segundo-conteo': (context) => const SegundoConteoScreenNew(),
            '/pattern-dashboard': (context) => const PatternDashboardScreen(),
            '/advanced-dashboard': (context) => const AdvancedDashboardScreen(),
            '/pdf': (context) => const PdfGeneratorScreen(),

            '/admin': (context) => const DataAdminScreen(),
            '/excel-preview': (context) => const ExcelPreviewScreen(),
            '/data-import': (context) => const DataImportScreen(),
            '/data-management': (context) => const DataManagementScreen(),
          },
        );
          },
        );
      },
    );
  }
}

