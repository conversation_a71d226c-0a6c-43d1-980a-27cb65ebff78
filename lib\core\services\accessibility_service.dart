import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Servicio para gestionar configuraciones de accesibilidad
class AccessibilityService extends ChangeNotifier {
  static const String _fontSizeKey = 'accessibility_font_size';
  static const String _highContrastKey = 'accessibility_high_contrast';
  static const String _reduceAnimationsKey = 'accessibility_reduce_animations';
  static const String _screenReaderKey = 'accessibility_screen_reader';
  static const String _hapticFeedbackKey = 'accessibility_haptic_feedback';
  static const String _colorBlindnessKey = 'accessibility_color_blindness';

  // Configuraciones de accesibilidad
  double _fontSize = 1.0;
  bool _highContrastEnabled = false;
  bool _reduceAnimationsEnabled = false;
  bool _screenReaderEnabled = false;
  bool _hapticFeedbackEnabled = true;
  String _colorBlindnessType = 'none';

  SharedPreferences? _prefs;

  // Getters
  double get fontSize => _fontSize;
  bool get highContrastEnabled => _highContrastEnabled;
  bool get reduceAnimationsEnabled => _reduceAnimationsEnabled;
  bool get screenReaderEnabled => _screenReaderEnabled;
  bool get hapticFeedbackEnabled => _hapticFeedbackEnabled;
  String get colorBlindnessType => _colorBlindnessType;

  /// Inicializar el servicio de accesibilidad
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadAccessibilityPreferences();
      print('✅ [ACCESSIBILITY] Servicio de accesibilidad inicializado');
    } catch (e) {
      print('❌ [ACCESSIBILITY] Error inicializando servicio: $e');
    }
  }

  /// Cargar preferencias de accesibilidad
  Future<void> _loadAccessibilityPreferences() async {
    try {
      _fontSize = _prefs?.getDouble(_fontSizeKey) ?? 1.0;
      _highContrastEnabled = _prefs?.getBool(_highContrastKey) ?? false;
      _reduceAnimationsEnabled = _prefs?.getBool(_reduceAnimationsKey) ?? false;
      _screenReaderEnabled = _prefs?.getBool(_screenReaderKey) ?? false;
      _hapticFeedbackEnabled = _prefs?.getBool(_hapticFeedbackKey) ?? true;
      _colorBlindnessType = _prefs?.getString(_colorBlindnessKey) ?? 'none';

      print('📱 [ACCESSIBILITY] Preferencias cargadas: fontSize=$_fontSize, highContrast=$_highContrastEnabled');
    } catch (e) {
      print('❌ [ACCESSIBILITY] Error cargando preferencias: $e');
    }
  }

  /// Guardar preferencias de accesibilidad
  Future<void> _saveAccessibilityPreferences() async {
    try {
      await _prefs?.setDouble(_fontSizeKey, _fontSize);
      await _prefs?.setBool(_highContrastKey, _highContrastEnabled);
      await _prefs?.setBool(_reduceAnimationsKey, _reduceAnimationsEnabled);
      await _prefs?.setBool(_screenReaderKey, _screenReaderEnabled);
      await _prefs?.setBool(_hapticFeedbackKey, _hapticFeedbackEnabled);
      await _prefs?.setString(_colorBlindnessKey, _colorBlindnessType);

      print('💾 [ACCESSIBILITY] Preferencias guardadas');
    } catch (e) {
      print('❌ [ACCESSIBILITY] Error guardando preferencias: $e');
    }
  }

  /// Establecer tamaño de fuente
  Future<void> setFontSize(double fontSize) async {
    if (_fontSize != fontSize) {
      _fontSize = fontSize.clamp(0.8, 1.5);
      await _saveAccessibilityPreferences();
      notifyListeners();
      print('🔤 [ACCESSIBILITY] Tamaño de fuente cambiado a: $_fontSize');
    }
  }

  /// Activar/desactivar alto contraste
  Future<void> setHighContrast(bool enabled) async {
    if (_highContrastEnabled != enabled) {
      _highContrastEnabled = enabled;
      await _saveAccessibilityPreferences();
      notifyListeners();
      print('🎨 [ACCESSIBILITY] Alto contraste: ${enabled ? "activado" : "desactivado"}');
    }
  }

  /// Activar/desactivar reducción de animaciones
  Future<void> setReduceAnimations(bool enabled) async {
    if (_reduceAnimationsEnabled != enabled) {
      _reduceAnimationsEnabled = enabled;
      await _saveAccessibilityPreferences();
      notifyListeners();
      print('🎬 [ACCESSIBILITY] Reducir animaciones: ${enabled ? "activado" : "desactivado"}');
    }
  }

  /// Activar/desactivar lector de pantalla
  Future<void> setScreenReader(bool enabled) async {
    if (_screenReaderEnabled != enabled) {
      _screenReaderEnabled = enabled;
      await _saveAccessibilityPreferences();
      notifyListeners();
      print('🔊 [ACCESSIBILITY] Lector de pantalla: ${enabled ? "activado" : "desactivado"}');
    }
  }

  /// Activar/desactivar retroalimentación háptica
  Future<void> setHapticFeedback(bool enabled) async {
    if (_hapticFeedbackEnabled != enabled) {
      _hapticFeedbackEnabled = enabled;
      await _saveAccessibilityPreferences();
      notifyListeners();
      print('📳 [ACCESSIBILITY] Retroalimentación háptica: ${enabled ? "activada" : "desactivada"}');
    }
  }

  /// Establecer tipo de daltonismo
  Future<void> setColorBlindnessType(String type) async {
    if (_colorBlindnessType != type) {
      _colorBlindnessType = type;
      await _saveAccessibilityPreferences();
      notifyListeners();
      print('🌈 [ACCESSIBILITY] Tipo de daltonismo cambiado a: $type');
    }
  }

  /// Obtener tema con configuraciones de accesibilidad aplicadas
  ThemeData getAccessibleTheme(ThemeData baseTheme) {
    ThemeData theme = baseTheme;

    // Aplicar tamaño de fuente
    if (_fontSize != 1.0) {
      theme = theme.copyWith(
        textTheme: theme.textTheme.apply(fontSizeFactor: _fontSize),
        primaryTextTheme: theme.primaryTextTheme.apply(fontSizeFactor: _fontSize),
      );
    }

    // Aplicar alto contraste
    if (_highContrastEnabled) {
      theme = theme.copyWith(
        colorScheme: theme.colorScheme.copyWith(
          primary: Colors.black,
          secondary: Colors.white,
          surface: Colors.white,
          onSurface: Colors.black,
        ),
      );
    }

    // Aplicar configuraciones de daltonismo
    if (_colorBlindnessType != 'none') {
      theme = _applyColorBlindnessFilter(theme);
    }

    return theme;
  }

  /// Aplicar filtro de daltonismo al tema
  ThemeData _applyColorBlindnessFilter(ThemeData theme) {
    // Implementación básica de filtros de daltonismo
    switch (_colorBlindnessType) {
      case 'protanopia':
        return theme.copyWith(
          colorScheme: theme.colorScheme.copyWith(
            primary: Colors.blue,
            secondary: Colors.yellow,
          ),
        );
      case 'deuteranopia':
        return theme.copyWith(
          colorScheme: theme.colorScheme.copyWith(
            primary: Colors.blue,
            secondary: Colors.orange,
          ),
        );
      case 'tritanopia':
        return theme.copyWith(
          colorScheme: theme.colorScheme.copyWith(
            primary: Colors.red,
            secondary: Colors.green,
          ),
        );
      default:
        return theme;
    }
  }

  /// Obtener duración de animación basada en configuración
  Duration getAnimationDuration(Duration defaultDuration) {
    return _reduceAnimationsEnabled 
        ? Duration(milliseconds: (defaultDuration.inMilliseconds * 0.3).round())
        : defaultDuration;
  }

  /// Verificar si se debe usar retroalimentación háptica
  bool shouldUseHapticFeedback() {
    return _hapticFeedbackEnabled;
  }

  /// Obtener estadísticas de accesibilidad
  Map<String, dynamic> getAccessibilityStats() {
    return {
      'font_size': _fontSize,
      'high_contrast_enabled': _highContrastEnabled,
      'reduce_animations_enabled': _reduceAnimationsEnabled,
      'screen_reader_enabled': _screenReaderEnabled,
      'haptic_feedback_enabled': _hapticFeedbackEnabled,
      'color_blindness_type': _colorBlindnessType,
      'accessibility_features_count': _getEnabledFeaturesCount(),
    };
  }

  /// Contar características de accesibilidad habilitadas
  int _getEnabledFeaturesCount() {
    int count = 0;
    if (_fontSize != 1.0) count++;
    if (_highContrastEnabled) count++;
    if (_reduceAnimationsEnabled) count++;
    if (_screenReaderEnabled) count++;
    if (!_hapticFeedbackEnabled) count++; // Contar como característica si está deshabilitado
    if (_colorBlindnessType != 'none') count++;
    return count;
  }

  /// Resetear a configuración por defecto
  Future<void> resetToDefault() async {
    _fontSize = 1.0;
    _highContrastEnabled = false;
    _reduceAnimationsEnabled = false;
    _screenReaderEnabled = false;
    _hapticFeedbackEnabled = true;
    _colorBlindnessType = 'none';
    
    await _saveAccessibilityPreferences();
    notifyListeners();
    print('🔄 [ACCESSIBILITY] Configuraciones restablecidas a valores por defecto');
  }
}
