import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/pattern_analysis_model.dart';
import '../utils/logger.dart';

/// Generador de datos de prueba para análisis de patrones - FASE 1
class TestDataGenerator {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final Random _random = Random();

  /// Generar datos de prueba para análisis de patrones
  static Future<void> generateTestData({int count = 100}) async {
    try {
      Logger.info('🧪 [TEST_DATA] Generando $count registros de prueba...');
      
      final batch = _firestore.batch();
      final now = DateTime.now();
      
      for (int i = 0; i < count; i++) {
        final data = _generateRandomConteo(now.subtract(Duration(days: _random.nextInt(90))));
        final docRef = _firestore.collection('segundos_conteos').doc();
        batch.set(docRef, data.toMap());
      }
      
      await batch.commit();
      Logger.info('✅ [TEST_DATA] $count registros generados exitosamente');
      
    } catch (e, stackTrace) {
      Logger.error('❌ [TEST_DATA] Error generando datos de prueba', e, stackTrace);
    }
  }

  /// Generar un conteo aleatorio
  static PatternAnalysisData _generateRandomConteo(DateTime fecha) {
    final placas = ['ABC123', 'DEF456', 'GHI789', 'JKL012', 'MNO345', 'PQR678', 'STU901', 'VWX234'];
    final verificadores = ['user1', 'user2', 'user3', 'user4', 'user5'];
    final conductores = ['conductor1', 'conductor2', 'conductor3', 'conductor4'];
    final rutas = ['Norte', 'Sur', 'Este', 'Oeste', 'Centro'];
    final skus = ['SKU001', 'SKU002', 'SKU003', 'SKU004', 'SKU005', 'SKU006', 'SKU007', 'SKU008'];
    
    // Patrones intencionales para testing
    bool tieneNovedad = false;
    String? tipoNovedad;
    List<String> skusConNovedad = [];
    
    // Patrón 1: Viernes tienen más novedades
    if (fecha.weekday == 5) { // Viernes
      tieneNovedad = _random.nextDouble() < 0.6; // 60% probabilidad
    }
    // Patrón 2: Horas pico (14-16) tienen más novedades
    else if (fecha.hour >= 14 && fecha.hour <= 16) {
      tieneNovedad = _random.nextDouble() < 0.5; // 50% probabilidad
    }
    // Patrón 3: Algunos verificadores son más precisos
    else if (verificadores[_random.nextInt(verificadores.length)] == 'user1') {
      tieneNovedad = _random.nextDouble() < 0.1; // 10% probabilidad (muy preciso)
    }
    else {
      tieneNovedad = _random.nextDouble() < 0.25; // 25% probabilidad normal
    }
    
    if (tieneNovedad) {
      tipoNovedad = _random.nextBool() ? 'Faltante' : 'Sobrante';
      skusConNovedad = [skus[_random.nextInt(skus.length)]];
      
      // Patrón 4: SKU001 es problemático
      if (_random.nextDouble() < 0.3) {
        skusConNovedad = ['SKU001'];
      }
    }
    
    return PatternAnalysisData(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(1000)}',
      vhId: 'VH_${_random.nextInt(1000)}',
      placa: placas[_random.nextInt(placas.length)],
      verificadorId: verificadores[_random.nextInt(verificadores.length)],
      conductorId: conductores[_random.nextInt(conductores.length)],
      ruta: rutas[_random.nextInt(rutas.length)],
      fechaConteo: fecha,
      diaSemana: fecha.weekday,
      hora: fecha.hour,
      cantidadSkus: _random.nextInt(20) + 5, // 5-25 SKUs
      pesoTotal: (_random.nextDouble() * 500) + 100, // 100-600 kg
      tieneNovedad: tieneNovedad,
      tipoNovedad: tipoNovedad,
      skusConNovedad: skusConNovedad,
      tiempoConteoMinutos: _random.nextInt(30) + 10, // 10-40 minutos
      metadatos: {
        'vhProgramados': _random.nextInt(50) + 10,
        'vhSalen': _random.nextInt(50) + 10,
        'totalNovedades': tieneNovedad ? 1 : 0,
        'generatedBy': 'TestDataGenerator',
      },
    );
  }

  /// Limpiar datos de prueba
  static Future<void> clearTestData() async {
    try {
      Logger.info('🧹 [TEST_DATA] Limpiando datos de prueba...');
      
      final querySnapshot = await _firestore
          .collection('segundos_conteos')
          .where('metadatos.generatedBy', isEqualTo: 'TestDataGenerator')
          .get();
      
      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      Logger.info('✅ [TEST_DATA] ${querySnapshot.docs.length} registros de prueba eliminados');
      
    } catch (e, stackTrace) {
      Logger.error('❌ [TEST_DATA] Error limpiando datos de prueba', e, stackTrace);
    }
  }

  /// Generar datos con patrones específicos para demostración
  static Future<void> generateDemoPatterns() async {
    try {
      Logger.info('🎭 [TEST_DATA] Generando patrones de demostración...');
      
      final batch = _firestore.batch();
      final now = DateTime.now();
      
      // Patrón 1: Viernes problemáticos (últimos 4 viernes)
      for (int week = 0; week < 4; week++) {
        final friday = now.subtract(Duration(days: now.weekday - 5 + (week * 7)));
        for (int i = 0; i < 10; i++) {
          final data = _generateRandomConteo(friday.add(Duration(hours: 8 + i)));
          // Forzar novedades en viernes
          final forcedData = PatternAnalysisData(
            id: data.id,
            vhId: data.vhId,
            placa: data.placa,
            verificadorId: data.verificadorId,
            conductorId: data.conductorId,
            ruta: data.ruta,
            fechaConteo: data.fechaConteo,
            diaSemana: data.diaSemana,
            hora: data.hora,
            cantidadSkus: data.cantidadSkus,
            pesoTotal: data.pesoTotal,
            tieneNovedad: _random.nextDouble() < 0.7, // 70% novedades en viernes
            tipoNovedad: 'Faltante',
            skusConNovedad: const ['SKU001'],
            tiempoConteoMinutos: data.tiempoConteoMinutos,
            metadatos: {...data.metadatos, 'pattern': 'friday_issues'},
          );
          
          final docRef = _firestore.collection('segundos_conteos').doc();
          batch.set(docRef, forcedData.toMap());
        }
      }
      
      // Patrón 2: Verificador problemático
      for (int day = 0; day < 30; day++) {
        final date = now.subtract(Duration(days: day));
        final data = _generateRandomConteo(date);
        
        final problematicData = PatternAnalysisData(
          id: data.id,
          vhId: data.vhId,
          placa: data.placa,
          verificadorId: 'user3', // Verificador específico
          conductorId: data.conductorId,
          ruta: data.ruta,
          fechaConteo: data.fechaConteo,
          diaSemana: data.diaSemana,
          hora: data.hora,
          cantidadSkus: data.cantidadSkus,
          pesoTotal: data.pesoTotal,
          tieneNovedad: _random.nextDouble() < 0.5, // 50% novedades para este verificador
          tipoNovedad: 'Sobrante',
          skusConNovedad: const ['SKU002'],
          tiempoConteoMinutos: data.tiempoConteoMinutos + 10, // Más lento
          metadatos: {...data.metadatos, 'pattern': 'problematic_verificador'},
        );
        
        final docRef = _firestore.collection('segundos_conteos').doc();
        batch.set(docRef, problematicData.toMap());
      }
      
      // Patrón 3: Hora pico problemática (2-4 PM)
      for (int day = 0; day < 20; day++) {
        final date = now.subtract(Duration(days: day));
        for (int hour = 14; hour <= 16; hour++) {
          final data = _generateRandomConteo(date.copyWith(hour: hour));
          
          final peakHourData = PatternAnalysisData(
            id: data.id,
            vhId: data.vhId,
            placa: data.placa,
            verificadorId: data.verificadorId,
            conductorId: data.conductorId,
            ruta: data.ruta,
            fechaConteo: data.fechaConteo,
            diaSemana: data.diaSemana,
            hora: hour,
            cantidadSkus: data.cantidadSkus,
            pesoTotal: data.pesoTotal,
            tieneNovedad: _random.nextDouble() < 0.6, // 60% novedades en hora pico
            tipoNovedad: 'Faltante',
            skusConNovedad: const ['SKU003'],
            tiempoConteoMinutos: data.tiempoConteoMinutos,
            metadatos: {...data.metadatos, 'pattern': 'peak_hour_issues'},
          );
          
          final docRef = _firestore.collection('segundos_conteos').doc();
          batch.set(docRef, peakHourData.toMap());
        }
      }
      
      await batch.commit();
      Logger.info('✅ [TEST_DATA] Patrones de demostración generados exitosamente');
      
    } catch (e, stackTrace) {
      Logger.error('❌ [TEST_DATA] Error generando patrones de demostración', e, stackTrace);
    }
  }

  /// Verificar si existen datos de prueba
  static Future<bool> hasTestData() async {
    try {
      final querySnapshot = await _firestore
          .collection('segundos_conteos')
          .where('metadatos.generatedBy', isEqualTo: 'TestDataGenerator')
          .limit(1)
          .get();
      
      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      Logger.error('❌ [TEST_DATA] Error verificando datos de prueba', e);
      return false;
    }
  }

  /// Obtener estadísticas de datos de prueba
  static Future<Map<String, dynamic>> getTestDataStats() async {
    try {
      final querySnapshot = await _firestore
          .collection('segundos_conteos')
          .where('metadatos.generatedBy', isEqualTo: 'TestDataGenerator')
          .get();
      
      int totalRecords = querySnapshot.docs.length;
      int recordsWithNovedades = 0;
      Map<String, int> patternCounts = {};
      
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        if (data['tieneNovedad'] == true) {
          recordsWithNovedades++;
        }
        
        final pattern = data['metadatos']?['pattern'];
        if (pattern != null) {
          patternCounts[pattern] = (patternCounts[pattern] ?? 0) + 1;
        }
      }
      
      return {
        'totalRecords': totalRecords,
        'recordsWithNovedades': recordsWithNovedades,
        'novedadPercentage': totalRecords > 0 ? (recordsWithNovedades / totalRecords * 100).toInt() : 0,
        'patternCounts': patternCounts,
      };
      
    } catch (e, stackTrace) {
      Logger.error('❌ [TEST_DATA] Error obteniendo estadísticas', e, stackTrace);
      return {};
    }
  }
}
