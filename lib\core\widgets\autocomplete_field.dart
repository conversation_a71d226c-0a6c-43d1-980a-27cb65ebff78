import 'dart:async';
import 'package:flutter/material.dart';

/// Widget de autocompletado optimizado y reutilizable
class AutocompleteField<T> extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final Future<Iterable<T>> Function(TextEditingValue) optionsBuilder;
  final String Function(T) displayStringForOption;
  final void Function(T) onSelected;
  final String? Function(String?)? validator;
  final Duration debounceDelay;
  final int maxOptions;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  const AutocompleteField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.hintText,
    required this.optionsBuilder,
    required this.displayStringForOption,
    required this.onSelected,
    this.validator,
    this.debounceDelay = const Duration(milliseconds: 300),
    this.maxOptions = 10,
    this.prefixIcon,
    this.suffixIcon,
  });

  @override
  State<AutocompleteField<T>> createState() => _AutocompleteFieldState<T>();
}

class _AutocompleteFieldState<T> extends State<AutocompleteField<T>> {
  Timer? _debounceTimer;
  List<T> _options = [];
  bool _isLoading = false;
  bool _showOptions = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _hideOptions();
    super.dispose();
  }

  void _onTextChanged(String value) {
    _debounceTimer?.cancel();
    
    if (value.isEmpty) {
      _hideOptions();
      return;
    }

    setState(() => _isLoading = true);

    _debounceTimer = Timer(widget.debounceDelay, () async {
      try {
        final options = await widget.optionsBuilder(TextEditingValue(text: value));
        if (mounted) {
          setState(() {
            _options = options.take(widget.maxOptions).toList();
            _isLoading = false;
          });
          
          if (_options.isNotEmpty) {
            _showOptionsOverlay();
          } else {
            _hideOptions();
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _options = [];
            _isLoading = false;
          });
          _hideOptions();
        }
      }
    });
  }

  void _showOptionsOverlay() {
    _hideOptions(); // Limpiar overlay anterior

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: _getFieldWidth(),
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 60),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: _options.length,
                itemBuilder: (context, index) {
                  final option = _options[index];
                  return ListTile(
                    dense: true,
                    title: Text(
                      widget.displayStringForOption(option),
                      style: const TextStyle(fontSize: 14),
                    ),
                    onTap: () {
                      widget.onSelected(option);
                      _hideOptions();
                    },
                    hoverColor: Colors.grey.shade100,
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() => _showOptions = true);
  }

  void _hideOptions() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (mounted) {
      setState(() => _showOptions = false);
    }
  }

  double _getFieldWidth() {
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    return renderBox?.size.width ?? MediaQuery.of(context).size.width - 32;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextFormField(
        controller: widget.controller,
        decoration: InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          prefixIcon: widget.prefixIcon,
          suffixIcon: _buildSuffixIcon(),
          border: const OutlineInputBorder(),
        ),
        validator: widget.validator,
        onChanged: _onTextChanged,
        onTap: () {
          if (widget.controller.text.isNotEmpty && _options.isNotEmpty) {
            _showOptionsOverlay();
          }
        },
      ),
    );
  }

  Widget _buildSuffixIcon() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(12),
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    if (_showOptions) {
      return IconButton(
        onPressed: _hideOptions,
        icon: const Icon(Icons.close, size: 20),
        tooltip: 'Cerrar sugerencias',
      );
    }

    return widget.suffixIcon ?? const Icon(Icons.search, size: 20);
  }
}

/// Widget de autocompletado simple para strings
class SimpleAutocompleteField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final List<String> options;
  final String? Function(String?)? validator;
  final void Function(String)? onSelected;

  const SimpleAutocompleteField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.hintText,
    required this.options,
    this.validator,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return AutocompleteField<String>(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      optionsBuilder: (textEditingValue) async {
        if (textEditingValue.text.isEmpty) return [];
        
        return options.where((option) =>
          option.toLowerCase().contains(textEditingValue.text.toLowerCase())
        );
      },
      displayStringForOption: (option) => option,
      onSelected: (option) {
        controller.text = option;
        onSelected?.call(option);
      },
      validator: validator,
    );
  }
}

/// Widget de autocompletado para mapas (clave-valor)
class MapAutocompleteField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final List<Map<String, String>> options;
  final String displayKey;
  final String valueKey;
  final String? Function(String?)? validator;
  final void Function(Map<String, String>)? onSelected;

  const MapAutocompleteField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.hintText,
    required this.options,
    required this.displayKey,
    required this.valueKey,
    this.validator,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return AutocompleteField<Map<String, String>>(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      optionsBuilder: (textEditingValue) async {
        if (textEditingValue.text.isEmpty) return [];
        
        return options.where((option) =>
          (option[displayKey] ?? '').toLowerCase().contains(textEditingValue.text.toLowerCase()) ||
          (option[valueKey] ?? '').toLowerCase().contains(textEditingValue.text.toLowerCase())
        );
      },
      displayStringForOption: (option) => option[displayKey] ?? '',
      onSelected: (option) {
        controller.text = option[valueKey] ?? '';
        onSelected?.call(option);
      },
      validator: validator,
    );
  }
}
