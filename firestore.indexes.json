{"indexes": [{"collectionGroup": "conteos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "verificador_uid", "order": "ASCENDING"}, {"fieldPath": "fecha", "order": "DESCENDING"}]}, {"collectionGroup": "conteos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "verificador_uid", "order": "ASCENDING"}, {"fieldPath": "tiene_novedad", "order": "ASCENDING"}, {"fieldPath": "fecha", "order": "DESCENDING"}]}, {"collectionGroup": "conteos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vh_id", "order": "ASCENDING"}, {"fieldPath": "verificador_uid", "order": "ASCENDING"}, {"fieldPath": "fecha", "order": "DESCENDING"}]}, {"collectionGroup": "vh_programados", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fecha", "order": "ASCENDING"}, {"fieldPath": "placa", "order": "ASCENDING"}]}, {"collectionGroup": "vh_programados", "queryScope": "COLLECTION", "fields": [{"fieldPath": "placa", "order": "ASCENDING"}, {"fieldPath": "fecha", "order": "ASCENDING"}]}, {"collectionGroup": "sku", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoria", "order": "ASCENDING"}, {"fieldPath": "descripcion", "order": "ASCENDING"}]}, {"collectionGroup": "sku", "queryScope": "COLLECTION", "fields": [{"fieldPath": "descripcion_lower", "order": "ASCENDING"}]}, {"collectionGroup": "auxiliares", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activo", "order": "ASCENDING"}, {"fieldPath": "nombre", "order": "ASCENDING"}]}, {"collectionGroup": "auxiliares", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activo", "order": "ASCENDING"}, {"fieldPath": "cargo", "order": "ASCENDING"}, {"fieldPath": "nombre", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "conteos", "fieldPath": "fecha", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "conteos", "fieldPath": "fecha_creacion", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "vh_programados", "fieldPath": "fecha", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "sku", "fieldPath": "descripcion", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "auxiliares", "fieldPath": "nombre", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}