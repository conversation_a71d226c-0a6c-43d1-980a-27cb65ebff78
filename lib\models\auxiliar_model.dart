import 'package:equatable/equatable.dart';

/// Modelo para representar un auxiliar/armador en el sistema
class AuxiliarModel extends Equatable {
  final String id;
  final String nombre;
  final String cedula;
  final String cargo;
  final String correo;
  final String telefono;
  final bool activo;
  final DateTime fechaCreacion;
  final DateTime? fechaActualizacion;

  const AuxiliarModel({
    required this.id,
    required this.nombre,
    required this.cedula,
    required this.cargo,
    this.correo = '',
    this.telefono = '',
    this.activo = true,
    required this.fechaCreacion,
    this.fechaActualizacion,
  });

  /// Crear desde Map (Firebase)
  factory AuxiliarModel.fromMap(Map<String, dynamic> map) {
    return AuxiliarModel(
      id: map['id'] as String? ?? '',
      nombre: map['nombre'] as String? ?? '',
      cedula: map['cedula'] as String? ?? '',
      cargo: map['cargo'] as String? ?? 'Auxiliar',
      correo: map['correo'] as String? ?? '',
      telefono: map['telefono'] as String? ?? '',
      activo: map['activo'] as bool? ?? true,
      fechaCreacion: map['fechaCreacion'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['fechaCreacion'])
          : DateTime.now(),
      fechaActualizacion: map['fechaActualizacion'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['fechaActualizacion'])
          : null,
    );
  }

  /// Convertir a Map (para Firebase)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nombre': nombre,
      'cedula': cedula,
      'cargo': cargo,
      'correo': correo,
      'telefono': telefono,
      'activo': activo,
      'fechaCreacion': fechaCreacion.millisecondsSinceEpoch,
      'fechaActualizacion': fechaActualizacion?.millisecondsSinceEpoch,
    };
  }

  /// Crear copia con cambios
  AuxiliarModel copyWith({
    String? id,
    String? nombre,
    String? cedula,
    String? cargo,
    String? correo,
    String? telefono,
    bool? activo,
    DateTime? fechaCreacion,
    DateTime? fechaActualizacion,
  }) {
    return AuxiliarModel(
      id: id ?? this.id,
      nombre: nombre ?? this.nombre,
      cedula: cedula ?? this.cedula,
      cargo: cargo ?? this.cargo,
      correo: correo ?? this.correo,
      telefono: telefono ?? this.telefono,
      activo: activo ?? this.activo,
      fechaCreacion: fechaCreacion ?? this.fechaCreacion,
      fechaActualizacion: fechaActualizacion ?? this.fechaActualizacion,
    );
  }

  /// Nombre completo para mostrar
  String get nombreCompleto => nombre.isNotEmpty ? nombre : 'Sin nombre';

  /// Información de contacto
  String get contacto {
    final List<String> info = [];
    if (correo.isNotEmpty) info.add(correo);
    if (telefono.isNotEmpty) info.add(telefono);
    return info.join(' • ');
  }

  /// Estado del auxiliar
  String get estado => activo ? 'Activo' : 'Inactivo';

  @override
  List<Object?> get props => [
        id,
        nombre,
        cedula,
        cargo,
        correo,
        telefono,
        activo,
        fechaCreacion,
        fechaActualizacion,
      ];

  @override
  String toString() {
    return 'AuxiliarModel(id: $id, nombre: $nombre, cedula: $cedula, cargo: $cargo, activo: $activo)';
  }
}
