import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Servicio de cache inteligente para optimizar consultas Firebase
/// 
/// Características:
/// - ✅ Cache con TTL (Time To Live)
/// - ✅ Invalidación automática
/// - ✅ Compresión de datos
/// - ✅ Límites de memoria
/// - ✅ Métricas de rendimiento
class SmartCacheService {
  static SmartCacheService? _instance;
  static SmartCacheService get instance => _instance ??= SmartCacheService._();
  
  SmartCacheService._();

  // Cache en memoria
  final Map<String, _CacheEntry> _memoryCache = {};
  
  // Configuración
  static const Duration _defaultTTL = Duration(minutes: 5);
  static const int _maxMemoryEntries = 100;
  static const int _maxDataSizeBytes = 1024 * 1024; // 1MB
  
  // Métricas
  int _hits = 0;
  int _misses = 0;
  int _evictions = 0;

  /// Obtener datos del cache
  Future<T?> get<T>(String key, {Duration? ttl}) async {
    try {
      // 1. Verificar cache en memoria
      final memoryResult = _getFromMemory<T>(key);
      if (memoryResult != null) {
        _hits++;
        Logger.debug('Cache HIT (memory): $key');
        return memoryResult;
      }

      // 2. Verificar cache persistente
      final persistentResult = await _getFromPersistent<T>(key, ttl ?? _defaultTTL);
      if (persistentResult != null) {
        _hits++;
        Logger.debug('Cache HIT (persistent): $key');
        
        // Promover a memoria para acceso rápido
        _setInMemory(key, persistentResult, ttl ?? _defaultTTL);
        return persistentResult;
      }

      _misses++;
      Logger.debug('Cache MISS: $key');
      return null;
    } catch (e, stackTrace) {
      Logger.error('Error getting from cache: $key', e, stackTrace);
      return null;
    }
  }

  /// Guardar datos en cache
  Future<void> set<T>(String key, T data, {Duration? ttl}) async {
    try {
      final effectiveTTL = ttl ?? _defaultTTL;
      
      // Guardar en memoria
      _setInMemory(key, data, effectiveTTL);
      
      // Guardar en persistente (async)
      _setPersistent(key, data, effectiveTTL);
      
      Logger.debug('Cache SET: $key (TTL: ${effectiveTTL.inMinutes}min)');
    } catch (e, stackTrace) {
      Logger.error('Error setting cache: $key', e, stackTrace);
    }
  }

  /// Invalidar entrada específica
  Future<void> invalidate(String key) async {
    try {
      _memoryCache.remove(key);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cache_$key');
      await prefs.remove('cache_meta_$key');
      
      Logger.debug('Cache INVALIDATED: $key');
    } catch (e, stackTrace) {
      Logger.error('Error invalidating cache: $key', e, stackTrace);
    }
  }

  /// Invalidar por patrón
  Future<void> invalidatePattern(String pattern) async {
    try {
      final regex = RegExp(pattern);
      final keysToRemove = _memoryCache.keys.where((key) => regex.hasMatch(key)).toList();
      
      for (final key in keysToRemove) {
        await invalidate(key);
      }
      
      Logger.debug('Cache INVALIDATED pattern: $pattern (${keysToRemove.length} entries)');
    } catch (e, stackTrace) {
      Logger.error('Error invalidating cache pattern: $pattern', e, stackTrace);
    }
  }

  /// Limpiar cache completo
  Future<void> clear() async {
    try {
      _memoryCache.clear();
      
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_')).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      _hits = 0;
      _misses = 0;
      _evictions = 0;
      
      Logger.info('Cache CLEARED completely');
    } catch (e, stackTrace) {
      Logger.error('Error clearing cache', e, stackTrace);
    }
  }

  /// Obtener métricas del cache
  Map<String, dynamic> getMetrics() {
    final totalRequests = _hits + _misses;
    final hitRate = totalRequests > 0 ? (_hits / totalRequests * 100) : 0.0;
    
    return {
      'hits': _hits,
      'misses': _misses,
      'evictions': _evictions,
      'hit_rate_percent': hitRate.toStringAsFixed(2),
      'memory_entries': _memoryCache.length,
      'max_memory_entries': _maxMemoryEntries,
      'memory_usage_bytes': _calculateMemoryUsage(),
      'max_memory_bytes': _maxDataSizeBytes,
    };
  }

  /// Limpiar entradas expiradas
  Future<void> cleanup() async {
    try {
      final now = DateTime.now();
      final expiredKeys = _memoryCache.entries
          .where((entry) => entry.value.expiresAt.isBefore(now))
          .map((entry) => entry.key)
          .toList();

      for (final key in expiredKeys) {
        _memoryCache.remove(key);
        _evictions++;
      }

      if (expiredKeys.isNotEmpty) {
        Logger.debug('Cache CLEANUP: removed ${expiredKeys.length} expired entries');
      }
    } catch (e, stackTrace) {
      Logger.error('Error during cache cleanup', e, stackTrace);
    }
  }

  // Métodos privados

  T? _getFromMemory<T>(String key) {
    final entry = _memoryCache[key];
    if (entry == null) return null;
    
    if (entry.expiresAt.isBefore(DateTime.now())) {
      _memoryCache.remove(key);
      _evictions++;
      return null;
    }
    
    return entry.data as T?;
  }

  Future<T?> _getFromPersistent<T>(String key, Duration ttl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString('cache_$key');
      final metaJson = prefs.getString('cache_meta_$key');
      
      if (dataJson == null || metaJson == null) return null;
      
      final meta = jsonDecode(metaJson) as Map<String, dynamic>;
      final expiresAt = DateTime.parse(meta['expires_at'] as String);
      
      if (expiresAt.isBefore(DateTime.now())) {
        // Expirado, limpiar
        await prefs.remove('cache_$key');
        await prefs.remove('cache_meta_$key');
        return null;
      }
      
      final data = jsonDecode(dataJson);
      return data as T?;
    } catch (e) {
      return null;
    }
  }

  void _setInMemory<T>(String key, T data, Duration ttl) {
    // Verificar límites de memoria
    if (_memoryCache.length >= _maxMemoryEntries) {
      _evictOldestEntry();
    }
    
    final entry = _CacheEntry(
      data: data,
      expiresAt: DateTime.now().add(ttl),
    );
    
    _memoryCache[key] = entry;
  }

  Future<void> _setPersistent<T>(String key, T data, Duration ttl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiresAt = DateTime.now().add(ttl);
      
      final dataJson = jsonEncode(data);
      final metaJson = jsonEncode({
        'expires_at': expiresAt.toIso8601String(),
        'size_bytes': dataJson.length,
      });
      
      await prefs.setString('cache_$key', dataJson);
      await prefs.setString('cache_meta_$key', metaJson);
    } catch (e) {
      // Ignorar errores de persistencia
    }
  }

  void _evictOldestEntry() {
    if (_memoryCache.isEmpty) return;
    
    // Encontrar la entrada más antigua
    String? oldestKey;
    DateTime? oldestTime;
    
    for (final entry in _memoryCache.entries) {
      if (oldestTime == null || entry.value.expiresAt.isBefore(oldestTime)) {
        oldestTime = entry.value.expiresAt;
        oldestKey = entry.key;
      }
    }
    
    if (oldestKey != null) {
      _memoryCache.remove(oldestKey);
      _evictions++;
    }
  }

  int _calculateMemoryUsage() {
    int totalBytes = 0;
    for (final entry in _memoryCache.values) {
      try {
        final json = jsonEncode(entry.data);
        totalBytes += json.length;
      } catch (e) {
        // Ignorar errores de serialización
      }
    }
    return totalBytes;
  }
}

/// Entrada del cache con metadatos
class _CacheEntry {
  final dynamic data;
  final DateTime expiresAt;
  
  _CacheEntry({
    required this.data,
    required this.expiresAt,
  });
}

/// Extensión para facilitar el uso del cache
extension SmartCacheExtension on SmartCacheService {
  /// Cache para consultas Firebase
  Future<T?> getFirebaseQuery<T>(String collection, Map<String, dynamic> params) async {
    final key = 'firebase_${collection}_${params.hashCode}';
    return await get<T>(key);
  }
  
  /// Guardar resultado de consulta Firebase
  Future<void> setFirebaseQuery<T>(String collection, Map<String, dynamic> params, T data) async {
    final key = 'firebase_${collection}_${params.hashCode}';
    await set(key, data, ttl: const Duration(minutes: 3)); // TTL más corto para datos Firebase
  }
  
  /// Invalidar cache de una colección
  Future<void> invalidateCollection(String collection) async {
    await invalidatePattern('firebase_${collection}_.*');
  }
}
