import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'app.dart';
import 'services/auth_service.dart';
import 'services/firebase_service.dart';
import 'core/services/app_state_service.dart';
import 'core/services/cache_service.dart';
import 'core/services/theme_service.dart';
import 'core/services/metrics_service.dart';
import 'core/services/localization_service.dart';

import 'core/repositories/conteo_repository.dart';
import 'core/repositories/sku_repository.dart';


void main() async {
  // ✅ MEJORA CRÍTICA: Manejo robusto de errores en inicialización
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Deshabilitar validación de Provider para desarrollo
    Provider.debugCheckInvalidValueType = null;

    // ✅ MEJORA CRÍTICA: Inicializar Firebase con manejo de errores
    await _initializeFirebase();

    // ✅ MEJORA CRÍTICA: Inicializar servicios con fallbacks
    await _initializeServices();

    // ✅ MEJORA CRÍTICA: Inicializar servicios de UI
    final services = await _initializeUIServices();

    _runApp(services);

  } catch (e, stackTrace) {
    // ✅ MEJORA CRÍTICA: Fallback si la inicialización falla
    print('❌ Error crítico en inicialización: $e');
    print('Stack trace: $stackTrace');
    _runFallbackApp();
  }
}

/// Inicializar Firebase con manejo de errores
Future<void> _initializeFirebase() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase inicializado correctamente');
  } catch (e) {
    print('❌ Error inicializando Firebase: $e');
    throw Exception('Firebase initialization failed: $e');
  }
}

/// Inicializar servicios críticos con fallbacks
Future<void> _initializeServices() async {
  final futures = <Future<void>>[];

  // Servicios críticos
  futures.add(_safeInitialize('CacheService', CacheService.initialize));

  // Esperar todos los servicios críticos
  await Future.wait(futures);
}

/// Inicializar servicios de UI
Future<Map<String, dynamic>> _initializeUIServices() async {
  final metricsService = MetricsService();
  await _safeInitialize('MetricsService', metricsService.initialize);

  final themeService = ThemeService();
  await _safeInitialize('ThemeService', themeService.initialize);

  final localizationService = LocalizationService();
  await _safeInitialize('LocalizationService', localizationService.initialize);

  return {
    'metricsService': metricsService,
    'themeService': themeService,
    'localizationService': localizationService,
  };
}

/// Ejecutar aplicación principal
void _runApp(Map<String, dynamic> services) {
  runApp(
    MultiProvider(
      providers: [
        // Servicios de estado
        ChangeNotifierProvider(create: (_) => AppStateService()),
        ChangeNotifierProvider<ThemeService>.value(value: services['themeService'] as ThemeService),
        ChangeNotifierProvider<MetricsService>.value(value: services['metricsService'] as MetricsService),
        ChangeNotifierProvider<LocalizationService>.value(value: services['localizationService'] as LocalizationService),
        ChangeNotifierProvider(create: (_) => AuthService()),
        Provider(create: (_) => FirebaseService()),

        // Repositorios
        Provider(create: (_) => ConteoRepository()),
        Provider(create: (_) => SkuRepository()),
        // Provider(create: (_) => AuxiliarRepository()), // Comentado hasta que exista
      ],
      child: const ReCountProApp(),
    ),
  );
}

/// Ejecutar aplicación de fallback en caso de error
void _runFallbackApp() {
  runApp(
    MaterialApp(
      title: 'ReCount Pro - Error',
      home: Scaffold(
        appBar: AppBar(
          title: const Text('ReCount Pro'),
          backgroundColor: Colors.red,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'Error de Inicialización',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'La aplicación no pudo inicializarse correctamente.\nPor favor, reinicia la aplicación.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

/// Inicializar servicio de forma segura con manejo de errores
Future<void> _safeInitialize(String serviceName, Future<void> Function() initFunction) async {
  try {
    await initFunction();
    print('✅ $serviceName inicializado correctamente');
  } catch (e) {
    print('⚠️ Error inicializando $serviceName: $e');
    // No lanzar excepción para permitir que otros servicios se inicialicen
  }
}