// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBOO5G1_5zlCvgBTygUhe180FR39t2Dz80',
    appId: '1:837728224978:web:c4cf68babe9c4fbd9fcf0b',
    messagingSenderId: '837728224978',
    projectId: 'recount-97f44',
    authDomain: 'recount-97f44.firebaseapp.com',
    storageBucket: 'recount-97f44.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBOO5G1_5zlCvgBTygUhe180FR39t2Dz80',
    appId: '1:837728224978:android:c4cf68babe9c4fbd9fcf0b',
    messagingSenderId: '837728224978',
    projectId: 'recount-97f44',
    authDomain: 'recount-97f44.firebaseapp.com',
    storageBucket: 'recount-97f44.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBOO5G1_5zlCvgBTygUhe180FR39t2Dz80',
    appId: '1:837728224978:ios:c4cf68babe9c4fbd9fcf0b',
    messagingSenderId: '837728224978',
    projectId: 'recount-97f44',
    authDomain: 'recount-97f44.firebaseapp.com',
    storageBucket: 'recount-97f44.firebasestorage.app',
    iosBundleId: 'com.example.recount_pro',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBOO5G1_5zlCvgBTygUhe180FR39t2Dz80',
    appId: '1:837728224978:ios:c4cf68babe9c4fbd9fcf0b',
    messagingSenderId: '837728224978',
    projectId: 'recount-97f44',
    authDomain: 'recount-97f44.firebaseapp.com',
    storageBucket: 'recount-97f44.firebasestorage.app',
    iosBundleId: 'com.example.recount_pro',
  );
}