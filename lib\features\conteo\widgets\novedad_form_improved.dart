import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../../services/firebase_service.dart';
import '../../../models/vh_model.dart';
import '../../../core/services/validation_service.dart';
import '../../../core/widgets/autocomplete_field.dart';

/// Formulario mejorado para registrar novedades con optimizaciones de rendimiento
class NovedadFormImproved extends StatefulWidget {
  final Function(NovedadConteo) onNovedadAdded;
  
  const NovedadFormImproved({
    super.key,
    required this.onNovedadAdded,
  });

  @override
  State<NovedadFormImproved> createState() => _NovedadFormImprovedState();
}

class _NovedadFormImprovedState extends State<NovedadFormImproved> {
  final _formKey = GlobalKey<FormState>();
  final _controllers = <String, TextEditingController>{};
  
  String _tipoNovedad = 'Faltante';
  int _diferencia = 0;
  List<String> _armadoresDisponibles = [];
  bool _isLoadingData = false;

  // Cache para datos frecuentemente consultados
  static final Map<String, List<Map<String, String>>> _cache = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static DateTime? _lastCacheUpdate;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _cargarDatos();
    _setupListeners();
  }

  void _initializeControllers() {
    final fields = ['dt', 'sku', 'descripcion', 'alistado', 'fisico', 'verificado', 'armador'];
    for (String field in fields) {
      _controllers[field] = TextEditingController();
    }
  }

  void _setupListeners() {
    _controllers['alistado']?.addListener(_calcularDiferencia);
    _controllers['fisico']?.addListener(_calcularDiferencia);
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _cargarDatos() async {
    if (_isLoadingData) return;
    
    setState(() => _isLoadingData = true);

    try {
      // Usar cache si está disponible y no ha expirado
      if (_isCacheValid()) {
        _armadoresDisponibles = _cache['armadores']?.map((e) => e['nombre'] ?? '').toList() ?? [];
        setState(() => _isLoadingData = false);
        return;
      }

      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      
      // Cargar datos en paralelo para mejor rendimiento
      final futures = await Future.wait([
        firebaseService.getAuxiliares(),
        _preloadSkuData(),
        _preloadVerificadoresData(),
      ]);

      final auxiliares = futures[0] as List<dynamic>;
      
      _armadoresDisponibles = auxiliares
          .where((aux) => aux.cargo.toLowerCase().contains('armador'))
          .map((aux) => aux.nombre as String)
          .toList();

      _updateCache();
      
    } catch (e) {
      _showError('Error cargando datos: $e');
    } finally {
      if (mounted) setState(() => _isLoadingData = false);
    }
  }

  bool _isCacheValid() {
    return _lastCacheUpdate != null && 
           DateTime.now().difference(_lastCacheUpdate!) < _cacheExpiry &&
           _cache.isNotEmpty;
  }

  void _updateCache() {
    _lastCacheUpdate = DateTime.now();
    // Cache se actualiza en los métodos de preload
  }

  Future<void> _preloadSkuData() async {
    try {
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      final skus = await firebaseService.buscarSkusIncremental('');
      _cache['skus'] = skus;
    } catch (e) {
      print('Warning: Could not preload SKU data: $e');
    }
  }

  Future<void> _preloadVerificadoresData() async {
    try {
      final firebaseService = Provider.of<FirebaseService>(context, listen: false);
      final verificadores = await firebaseService.buscarVerificadoresIncremental('');
      _cache['verificadores'] = verificadores;
    } catch (e) {
      print('Warning: Could not preload verificadores data: $e');
    }
  }

  void _calcularDiferencia() {
    final alistado = int.tryParse(_controllers['alistado']?.text ?? '') ?? 0;
    final fisico = int.tryParse(_controllers['fisico']?.text ?? '') ?? 0;
    
    setState(() {
      _diferencia = fisico - alistado;
      _tipoNovedad = _diferencia < 0 ? 'Faltante' : 'Sobrante';
    });
  }

  void _agregarNovedad() {
    if (!_formKey.currentState!.validate()) return;
    
    final novedad = NovedadConteo(
      tipo: _tipoNovedad,
      dt: _controllers['dt']!.text.trim(),
      sku: _controllers['sku']!.text.trim(),
      descripcion: _controllers['descripcion']!.text.trim(),
      alistado: int.parse(_controllers['alistado']!.text),
      fisico: int.parse(_controllers['fisico']!.text),
      diferencia: _diferencia,
      verificado: _controllers['verificado']!.text.trim(), // ✅ CORREGIDO: Usar el nombre del verificador
      armador: '${_controllers['armador']!.text.trim()} | Verificado por: ${_controllers['verificado']!.text.trim()}',
    );
    
    widget.onNovedadAdded(novedad);
    _limpiarFormulario();
    _showSuccess('Novedad agregada exitosamente');
  }

  void _limpiarFormulario() {
    for (var controller in _controllers.values) {
      controller.clear();
    }
    setState(() {
      _tipoNovedad = 'Faltante';
      _diferencia = 0;
    });
  }

  void _showError(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppTheme.errorColor),
    );
  }

  void _showSuccess(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppTheme.successColor),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 16),
              _buildTipoNovedad(),
              const SizedBox(height: 16),
              _buildDTField(),
              const SizedBox(height: 16),
              _buildSkuField(),
              const SizedBox(height: 16),
              _buildDescripcionField(),
              const SizedBox(height: 16),
              _buildCantidadesRow(),
              const SizedBox(height: 16),
              _buildVerificacionRow(),
              const SizedBox(height: 20),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return const Text(
      'Registrar Novedad',
      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildTipoNovedad() {
    return Row(
      children: [
        const Text('Tipo: '),
        const SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _tipoNovedad == 'Faltante' ? AppTheme.errorColor : Colors.orange,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _tipoNovedad,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildDTField() {
    return TextFormField(
      controller: _controllers['dt'],
      decoration: const InputDecoration(
        labelText: 'DT',
        hintText: 'Número de documento de transporte',
      ),
      validator: (value) {
        final result = ValidationService.validateRequired(value, 'DT');
        return result.isValid ? null : result.errorMessage;
      },
    );
  }

  Widget _buildSkuField() {
    return AutocompleteField<Map<String, String>>(
      controller: _controllers['sku']!,
      labelText: 'SKU',
      hintText: 'Buscar código del producto...',
      optionsBuilder: (textEditingValue) async {
        if (textEditingValue.text.isEmpty) return [];
        
        final cached = _cache['skus'] ?? [];
        return cached.where((sku) =>
          sku['sku']!.toLowerCase().contains(textEditingValue.text.toLowerCase()) ||
          sku['descripcion']!.toLowerCase().contains(textEditingValue.text.toLowerCase())
        ).toList();
      },
      displayStringForOption: (option) => option['sku'] ?? '',
      onSelected: (option) {
        _controllers['sku']!.text = option['sku'] ?? '';
        _controllers['descripcion']!.text = option['descripcion'] ?? '';
      },
      validator: (value) {
        final result = ValidationService.validateSku(value);
        return result.isValid ? null : result.errorMessage;
      },
    );
  }

  Widget _buildDescripcionField() {
    return TextFormField(
      controller: _controllers['descripcion'],
      decoration: const InputDecoration(
        labelText: 'Descripción',
        hintText: 'Se completa automáticamente al seleccionar SKU',
        suffixIcon: Icon(Icons.auto_fix_high, size: 20),
      ),
      readOnly: true,
      style: TextStyle(color: Colors.grey.shade700, fontStyle: FontStyle.italic),
      validator: (value) => value?.trim().isEmpty == true 
          ? 'Selecciona un SKU para completar la descripción' 
          : null,
    );
  }

  Widget _buildCantidadesRow() {
    return Row(
      children: [
        Expanded(child: _buildQuantityField('alistado', 'Alistado')),
        const SizedBox(width: 12),
        Expanded(child: _buildQuantityField('fisico', 'Físico')),
        const SizedBox(width: 12),
        Expanded(child: _buildDiferenciaDisplay()),
      ],
    );
  }

  Widget _buildQuantityField(String key, String label) {
    return TextFormField(
      controller: _controllers[key],
      decoration: InputDecoration(labelText: label),
      keyboardType: TextInputType.number,
      validator: (value) {
        final result = ValidationService.validateQuantity(value);
        return result.isValid ? null : result.errorMessage;
      },
    );
  }

  Widget _buildDiferenciaDisplay() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          const Text('Diferencia', style: TextStyle(fontSize: 12, color: Colors.grey)),
          Text(
            _diferencia.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _diferencia == 0 ? Colors.green 
                  : _diferencia < 0 ? AppTheme.errorColor : Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificacionRow() {
    return Row(
      children: [
        Expanded(child: _buildVerificadorField()),
        const SizedBox(width: 12),
        Expanded(child: _buildArmadorField()),
      ],
    );
  }

  Widget _buildVerificadorField() {
    return AutocompleteField<Map<String, String>>(
      controller: _controllers['verificado']!,
      labelText: 'Verificado por',
      hintText: 'Buscar verificador...',
      optionsBuilder: (textEditingValue) async {
        if (textEditingValue.text.isEmpty) return [];
        
        final cached = _cache['verificadores'] ?? [];
        return cached.where((verificador) =>
          verificador['nombre']!.toLowerCase().contains(textEditingValue.text.toLowerCase())
        ).toList();
      },
      displayStringForOption: (option) => option['nombre'] ?? '',
      onSelected: (option) {
        _controllers['verificado']!.text = option['nombre'] ?? '';
      },
      validator: (value) => value?.trim().isEmpty == true 
          ? 'Por favor ingrese el verificador' 
          : null,
    );
  }

  Widget _buildArmadorField() {
    return _isLoadingData
        ? const Center(child: CircularProgressIndicator())
        : DropdownButtonFormField<String>(
            value: _controllers['armador']!.text.isEmpty ? null : _controllers['armador']!.text,
            decoration: const InputDecoration(labelText: 'Armador'),
            items: _armadoresDisponibles
                .map((armador) => DropdownMenuItem(value: armador, child: Text(armador)))
                .toList(),
            onChanged: (value) => _controllers['armador']!.text = value ?? '',
            validator: (value) => value?.isEmpty == true ? 'Selecciona armador' : null,
          );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _limpiarFormulario,
            child: const Text('Limpiar'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _agregarNovedad,
            child: const Text('Agregar Novedad'),
          ),
        ),
      ],
    );
  }
}
