import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Servicio para gestionar el estado de sincronización
class SyncService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const String _syncStatusKey = 'sync_status';
  
  /// Obtener estado de sincronización de todas las colecciones
  Future<Map<String, dynamic>> getSyncStatus() async {
    try {
      print('🔄 [SYNC] Obteniendo estado de sincronización...');
      
      final prefs = await SharedPreferences.getInstance();
      final lastSyncTimestamp = prefs.getInt(_lastSyncKey) ?? 0;
      final lastSyncDate = DateTime.fromMillisecondsSinceEpoch(lastSyncTimestamp);
      
      // Verificar conexión a Firebase
      final isConnected = await _checkFirebaseConnection();
      
      // Verificar estado de cada colección
      final collections = {
        'segundo_conteo': 'Datos de conteo',
        'users': 'Información de usuario', 
        'sku': 'Catálogo de SKUs',
        'vh_programados': 'Lista de VH',
      };
      
      final collectionStatus = <String, Map<String, dynamic>>{};
      
      for (final entry in collections.entries) {
        final collectionName = entry.key;
        final displayName = entry.value;
        
        try {
          // Verificar si la colección existe y tiene datos
          final snapshot = await _firestore
              .collection(collectionName)
              .limit(1)
              .get();
          
          final hasData = snapshot.docs.isNotEmpty;
          final lastUpdate = await _getLastUpdateTime(collectionName);
          
          collectionStatus[collectionName] = {
            'display_name': displayName,
            'is_synced': hasData && isConnected,
            'last_update': lastUpdate,
            'status_text': hasData && isConnected ? 'Sincronizado' : 'Pendiente',
          };
          
        } catch (e) {
          collectionStatus[collectionName] = {
            'display_name': displayName,
            'is_synced': false,
            'last_update': null,
            'status_text': 'Error',
          };
        }
      }
      
      // Calcular tiempo desde última sincronización
      final timeSinceSync = _getTimeSinceLastSync(lastSyncDate);
      
      final syncStatus = {
        'is_connected': isConnected,
        'last_sync': lastSyncDate,
        'time_since_sync': timeSinceSync,
        'collections': collectionStatus,
        'connection_status': isConnected ? 'Conectado' : 'Desconectado',
      };
      
      print('✅ [SYNC] Estado de sincronización obtenido: $syncStatus');
      return syncStatus;
      
    } catch (e) {
      print('❌ [SYNC] Error obteniendo estado de sincronización: $e');
      return {
        'is_connected': false,
        'last_sync': DateTime.now().subtract(const Duration(hours: 24)),
        'time_since_sync': 'Error',
        'collections': <String, Map<String, dynamic>>{},
        'connection_status': 'Error',
      };
    }
  }
  
  /// Verificar conexión a Firebase
  Future<bool> _checkFirebaseConnection() async {
    try {
      // Intentar una operación simple para verificar conectividad
      await _firestore
          .collection('_connection_test')
          .limit(1)
          .get()
          .timeout(const Duration(seconds: 5));
      return true;
    } catch (e) {
      print('⚠️ [SYNC] Sin conexión a Firebase: $e');
      return false;
    }
  }
  
  /// Obtener tiempo de última actualización de una colección
  Future<DateTime?> _getLastUpdateTime(String collectionName) async {
    try {
      final snapshot = await _firestore
          .collection(collectionName)
          .orderBy('created_at', descending: true)
          .limit(1)
          .get();
      
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data();
        final timestamp = data['created_at'] as Timestamp?;
        return timestamp?.toDate();
      }
      
      return null;
    } catch (e) {
      // Si no hay campo created_at, usar timestamp del documento
      try {
        final snapshot = await _firestore
            .collection(collectionName)
            .limit(1)
            .get();
        
        if (snapshot.docs.isNotEmpty) {
          return snapshot.docs.first.metadata.hasPendingWrites 
              ? DateTime.now() 
              : DateTime.now().subtract(const Duration(hours: 1));
        }
      } catch (e2) {
        print('⚠️ [SYNC] Error obteniendo última actualización de $collectionName: $e2');
      }
      return null;
    }
  }
  
  /// Calcular tiempo transcurrido desde la última sincronización
  String _getTimeSinceLastSync(DateTime lastSync) {
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    if (difference.inMinutes < 1) {
      return 'Hace menos de 1 minuto';
    } else if (difference.inMinutes < 60) {
      return 'Hace ${difference.inMinutes} minutos';
    } else if (difference.inHours < 24) {
      return 'Hace ${difference.inHours} horas';
    } else {
      return 'Hace ${difference.inDays} días';
    }
  }
  
  /// Realizar sincronización manual
  Future<bool> performManualSync() async {
    try {
      print('🔄 [SYNC] Iniciando sincronización manual...');
      
      // Verificar conexión
      final isConnected = await _checkFirebaseConnection();
      if (!isConnected) {
        throw Exception('Sin conexión a Firebase');
      }
      
      // Simular proceso de sincronización
      await Future.delayed(const Duration(seconds: 2));
      
      // Actualizar timestamp de última sincronización
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
      
      print('✅ [SYNC] Sincronización manual completada');
      return true;
      
    } catch (e) {
      print('❌ [SYNC] Error en sincronización manual: $e');
      return false;
    }
  }
}
