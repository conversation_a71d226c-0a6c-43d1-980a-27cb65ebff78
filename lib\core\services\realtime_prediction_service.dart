import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../models/ml_models.dart';
import 'advanced_ml_service.dart';
import '../utils/logger.dart';

/// Servicio de predicciones en tiempo real - FASE 2
/// Maneja predicciones continuas y notificaciones en tiempo real
class RealtimePredictionService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AdvancedMLService _mlService;
  
  // Streams y subscripciones
  StreamSubscription<QuerySnapshot>? _conteoSubscription;
  StreamSubscription<QuerySnapshot>? _vhSubscription;
  
  // Cache de predicciones activas
  final Map<String, AdvancedPrediction> _activePredictions = {};
  final Map<String, StreamController<AdvancedPrediction>> _predictionStreams = {};
  
  // Configuración
  static const Duration _predictionInterval = Duration(seconds: 30);
  static const Duration _cacheExpiry = Duration(minutes: 10);
  
  // Estado del servicio
  bool _isActive = false;
  Timer? _predictionTimer;
  DateTime? _lastUpdate;
  
  // Métricas en tiempo real
  int _totalPredictions = 0;
  int _highRiskPredictions = 0;
  double _averageAccuracy = 0.0;
  
  // Getters
  bool get isActive => _isActive;
  Map<String, AdvancedPrediction> get activePredictions => Map.unmodifiable(_activePredictions);
  DateTime? get lastUpdate => _lastUpdate;
  int get totalPredictions => _totalPredictions;
  int get highRiskPredictions => _highRiskPredictions;
  double get averageAccuracy => _averageAccuracy;

  RealtimePredictionService(this._mlService);

  /// Inicializar el servicio de predicciones en tiempo real
  Future<void> initialize() async {
    try {
      Logger.info('🔄 [REALTIME_PRED] Inicializando servicio de predicciones en tiempo real...');
      
      // Asegurar que el servicio ML esté inicializado
      if (!_mlService.isInitialized) {
        await _mlService.initialize();
      }
      
      // Iniciar monitoreo de cambios
      await _startRealtimeMonitoring();
      
      // Iniciar timer de predicciones periódicas
      _startPredictionTimer();
      
      _isActive = true;
      _lastUpdate = DateTime.now();
      
      Logger.info('✅ [REALTIME_PRED] Servicio inicializado correctamente');
      notifyListeners();
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error inicializando servicio', e, stackTrace);
    }
  }

  /// Detener el servicio
  @override
  Future<void> dispose() async {
    try {
      Logger.info('🛑 [REALTIME_PRED] Deteniendo servicio...');
      
      _isActive = false;
      
      // Cancelar subscripciones
      await _conteoSubscription?.cancel();
      await _vhSubscription?.cancel();
      
      // Cancelar timer
      _predictionTimer?.cancel();
      
      // Cerrar streams
      for (final controller in _predictionStreams.values) {
        await controller.close();
      }
      _predictionStreams.clear();
      
      // Limpiar cache
      _activePredictions.clear();
      
      Logger.info('✅ [REALTIME_PRED] Servicio detenido correctamente');
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error deteniendo servicio', e, stackTrace);
    }
    
    super.dispose();
  }

  /// Obtener stream de predicciones para un VH específico
  Stream<AdvancedPrediction> getPredictionStream(String vhId) {
    if (!_predictionStreams.containsKey(vhId)) {
      _predictionStreams[vhId] = StreamController<AdvancedPrediction>.broadcast();
    }
    
    return _predictionStreams[vhId]!.stream;
  }

  /// Solicitar predicción inmediata para un VH
  Future<AdvancedPrediction> requestImmediatePrediction({
    required String vhId,
    required String verificadorId,
    String? ruta,
    List<String>? skus,
    Map<String, dynamic>? contextData,
  }) async {
    try {
      Logger.info('⚡ [REALTIME_PRED] Predicción inmediata solicitada para VH: $vhId');
      
      final prediction = await _mlService.predictAdvanced(
        vhId: vhId,
        verificadorId: verificadorId,
        fechaSalida: DateTime.now(),
        ruta: ruta,
        skus: skus,
        contextData: contextData,
      );
      
      // Actualizar cache
      _activePredictions[vhId] = prediction;
      
      // Enviar a stream si existe
      if (_predictionStreams.containsKey(vhId)) {
        _predictionStreams[vhId]!.add(prediction);
      }
      
      // Actualizar métricas
      _updateMetrics(prediction);
      
      _lastUpdate = DateTime.now();
      notifyListeners();
      
      return prediction;
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error en predicción inmediata', e, stackTrace);
      return AdvancedPrediction.defaultPrediction(vhId, verificadorId);
    }
  }

  /// Iniciar monitoreo en tiempo real de cambios en Firebase
  Future<void> _startRealtimeMonitoring() async {
    try {
      Logger.info('👁️ [REALTIME_PRED] Iniciando monitoreo en tiempo real...');
      
      // Monitorear cambios en segundos conteos
      _conteoSubscription = _firestore
          .collection('segundos_conteos')
          .where('fecha', isGreaterThan: Timestamp.fromDate(DateTime.now().subtract(const Duration(hours: 24))))
          .snapshots()
          .listen(_onConteoChange);
      
      // Monitorear cambios en VH activos
      _vhSubscription = _firestore
          .collection('vh')
          .where('estado', isEqualTo: 'activo')
          .snapshots()
          .listen(_onVhChange);
      
      Logger.info('✅ [REALTIME_PRED] Monitoreo iniciado correctamente');
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error iniciando monitoreo', e, stackTrace);
    }
  }

  /// Manejar cambios en conteos
  void _onConteoChange(QuerySnapshot snapshot) {
    try {
      Logger.info('📊 [REALTIME_PRED] Cambios detectados en conteos: ${snapshot.docChanges.length}');
      
      for (final change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.added || change.type == DocumentChangeType.modified) {
          final data = change.doc.data() as Map<String, dynamic>;
          _processConteoChange(change.doc.id, data);
        }
      }
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error procesando cambios de conteo', e, stackTrace);
    }
  }

  /// Manejar cambios en VH
  void _onVhChange(QuerySnapshot snapshot) {
    try {
      Logger.info('🚛 [REALTIME_PRED] Cambios detectados en VH: ${snapshot.docChanges.length}');
      
      for (final change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.added || change.type == DocumentChangeType.modified) {
          final data = change.doc.data() as Map<String, dynamic>;
          _processVhChange(change.doc.id, data);
        }
      }
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error procesando cambios de VH', e, stackTrace);
    }
  }

  /// Procesar cambio en conteo
  void _processConteoChange(String conteoId, Map<String, dynamic> data) {
    try {
      final vhId = data['vhId'] as String?;
      final verificadorId = data['verificadorUid'] as String?;
      
      if (vhId != null && verificadorId != null) {
        // Generar nueva predicción basada en el cambio
        _generatePredictionFromChange(vhId, verificadorId, data);
      }
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error procesando cambio de conteo', e, stackTrace);
    }
  }

  /// Procesar cambio en VH
  void _processVhChange(String vhId, Map<String, dynamic> data) {
    try {
      final placa = data['placa'] as String?;
      final estado = data['estado'] as String?;
      
      if (placa != null && estado == 'activo') {
        // VH activado, generar predicción proactiva
        Logger.info('🚛 [REALTIME_PRED] VH activado: $placa');
        // Aquí se podría generar una predicción proactiva
      }
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error procesando cambio de VH', e, stackTrace);
    }
  }

  /// Generar predicción basada en cambio
  Future<void> _generatePredictionFromChange(String vhId, String verificadorId, Map<String, dynamic> data) async {
    try {
      final contextData = {
        'tieneNovedad': data['tieneNovedad'] ?? false,
        'tipoNovedad': data['tipoNovedad'],
        'cantidadSkus': (data['productos'] as List?)?.length ?? 0,
        'ruta': data['ruta'],
      };
      
      final prediction = await _mlService.predictAdvanced(
        vhId: vhId,
        verificadorId: verificadorId,
        fechaSalida: DateTime.now(),
        ruta: data['ruta'],
        contextData: contextData,
      );
      
      // Actualizar cache y notificar
      _activePredictions[vhId] = prediction;
      
      if (_predictionStreams.containsKey(vhId)) {
        _predictionStreams[vhId]!.add(prediction);
      }
      
      _updateMetrics(prediction);
      notifyListeners();
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error generando predicción desde cambio', e, stackTrace);
    }
  }

  /// Iniciar timer de predicciones periódicas
  void _startPredictionTimer() {
    _predictionTimer = Timer.periodic(_predictionInterval, (_) {
      _runPeriodicPredictions();
    });
  }

  /// Ejecutar predicciones periódicas
  Future<void> _runPeriodicPredictions() async {
    if (!_isActive) return;
    
    try {
      Logger.info('⏰ [REALTIME_PRED] Ejecutando predicciones periódicas...');
      
      // Limpiar cache expirado
      _cleanExpiredCache();
      
      // Obtener VH activos para predicción
      final activeVh = await _getActiveVh();
      
      for (final vh in activeVh) {
        await _generatePeriodicPrediction(vh);
      }
      
      _lastUpdate = DateTime.now();
      notifyListeners();
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error en predicciones periódicas', e, stackTrace);
    }
  }

  /// Obtener VH activos
  Future<List<Map<String, dynamic>>> _getActiveVh() async {
    try {
      final snapshot = await _firestore
          .collection('vh')
          .where('estado', isEqualTo: 'activo')
          .limit(10) // Limitar para no sobrecargar
          .get();
      
      return snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error obteniendo VH activos', e, stackTrace);
      return [];
    }
  }

  /// Generar predicción periódica para un VH
  Future<void> _generatePeriodicPrediction(Map<String, dynamic> vh) async {
    try {
      final vhId = vh['id'] as String;
      final verificadorId = vh['verificadorAsignado'] as String? ?? 'default';
      
      // Solo generar si no hay predicción reciente
      final existingPrediction = _activePredictions[vhId];
      if (existingPrediction != null) {
        final timeDiff = DateTime.now().difference(existingPrediction.fechaPrediccion);
        if (timeDiff < _predictionInterval) {
          return; // Predicción muy reciente
        }
      }
      
      final prediction = await _mlService.predictAdvanced(
        vhId: vhId,
        verificadorId: verificadorId,
        fechaSalida: DateTime.now(),
        ruta: vh['ruta'],
        contextData: vh,
      );
      
      _activePredictions[vhId] = prediction;
      
      if (_predictionStreams.containsKey(vhId)) {
        _predictionStreams[vhId]!.add(prediction);
      }
      
      _updateMetrics(prediction);
      
    } catch (e, stackTrace) {
      Logger.error('❌ [REALTIME_PRED] Error en predicción periódica', e, stackTrace);
    }
  }

  /// Limpiar cache expirado
  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _activePredictions.entries) {
      final timeDiff = now.difference(entry.value.fechaPrediccion);
      if (timeDiff > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _activePredictions.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      Logger.info('🧹 [REALTIME_PRED] Cache limpiado: ${expiredKeys.length} predicciones expiradas');
    }
  }

  /// Actualizar métricas del servicio
  void _updateMetrics(AdvancedPrediction prediction) {
    _totalPredictions++;
    
    if (prediction.probabilidadNovedad > 0.7) {
      _highRiskPredictions++;
    }
    
    // Actualizar accuracy promedio (simplificado)
    _averageAccuracy = (_averageAccuracy * (_totalPredictions - 1) + prediction.confianza) / _totalPredictions;
  }

  /// Obtener estadísticas del servicio
  Map<String, dynamic> getRealtimeStats() {
    return {
      'isActive': _isActive,
      'lastUpdate': _lastUpdate?.toIso8601String(),
      'activePredictions': _activePredictions.length,
      'totalPredictions': _totalPredictions,
      'highRiskPredictions': _highRiskPredictions,
      'averageAccuracy': _averageAccuracy,
      'activeStreams': _predictionStreams.length,
    };
  }

  /// Obtener predicciones de alto riesgo
  List<AdvancedPrediction> getHighRiskPredictions() {
    return _activePredictions.values
        .where((p) => p.probabilidadNovedad > 0.7)
        .toList()
      ..sort((a, b) => b.probabilidadNovedad.compareTo(a.probabilidadNovedad));
  }

  /// Obtener alertas activas
  List<String> getActiveAlerts() {
    final alerts = <String>[];
    
    for (final prediction in _activePredictions.values) {
      if (prediction.probabilidadNovedad > 0.8) {
        alerts.add('🚨 VH ${prediction.vhId}: ${(prediction.probabilidadNovedad * 100).toInt()}% probabilidad de novedad');
      }
      
      for (final alert in prediction.demandaPrediccion.alertas) {
        alerts.add('📊 VH ${prediction.vhId}: $alert');
      }
    }
    
    return alerts;
  }
}
