import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/services/pattern_analysis_service.dart';
import '../../models/pattern_analysis_model.dart';
import '../../core/theme/app_theme.dart';

/// Pantalla del dashboard de análisis de patrones - FASE 1
class PatternDashboardScreen extends StatefulWidget {
  const PatternDashboardScreen({super.key});

  @override
  State<PatternDashboardScreen> createState() => _PatternDashboardScreenState();
}

class _PatternDashboardScreenState extends State<PatternDashboardScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeAnalysis();
  }

  Future<void> _initializeAnalysis() async {
    setState(() => _isLoading = true);
    
    final patternService = context.read<PatternAnalysisService>();
    if (!patternService.hasRecentAnalysis) {
      await patternService.initialize();
    }
    
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.white),
            SizedBox(width: 8),
            Text('🤖 Análisis Inteligente'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAnalysis,
            tooltip: 'Actualizar análisis',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('🧠 Analizando patrones...'),
                  SizedBox(height: 8),
                  Text(
                    'Esto puede tomar unos momentos',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            )
          : Consumer<PatternAnalysisService>(
              builder: (context, patternService, child) {
                return RefreshIndicator(
                  onRefresh: _refreshAnalysis,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSummaryCard(patternService),
                        const SizedBox(height: 16),
                        _buildPatternsSection(patternService),
                        const SizedBox(height: 16),
                        _buildRecommendationsSection(patternService),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }

  Widget _buildSummaryCard(PatternAnalysisService service) {
    final summary = service.getPatternSummary();
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text(
                  'Resumen del Análisis',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    '📊 Patrones Detectados',
                    '${summary['totalPatrones']}',
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    '📈 Datos Analizados',
                    '${summary['datosAnalizados']}',
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (summary['ultimoAnalisis'] != null)
              Text(
                '🕒 Último análisis: ${_formatDateTime(summary['ultimoAnalisis'])}',
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPatternsSection(PatternAnalysisService service) {
    final patterns = service.detectedPatterns;
    
    if (patterns.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Icon(
                Icons.search_off,
                size: 48,
                color: Colors.grey,
              ),
              const SizedBox(height: 8),
              const Text(
                'No se detectaron patrones',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                'Se necesitan más datos para el análisis',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    // Agrupar patrones por tipo
    final groupedPatterns = <String, List<DetectedPattern>>{};
    for (final pattern in patterns) {
      groupedPatterns.putIfAbsent(pattern.tipo, () => []).add(pattern);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '🔍 Patrones Detectados',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...groupedPatterns.entries.map((entry) => 
          _buildPatternGroup(entry.key, entry.value)
        ),
      ],
    );
  }

  Widget _buildPatternGroup(String tipo, List<DetectedPattern> patterns) {
    final icon = _getPatternIcon(tipo);
    final title = _getPatternTitle(tipo);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(icon, color: AppTheme.primaryColor),
        title: Text(
          '$title (${patterns.length})',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        children: patterns.map((pattern) => _buildPatternTile(pattern)).toList(),
      ),
    );
  }

  Widget _buildPatternTile(DetectedPattern pattern) {
    final impactColor = _getImpactColor(pattern.impacto);
    
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: impactColor.withValues(alpha: 0.2),
        child: Icon(
          Icons.trending_up,
          color: impactColor,
          size: 20,
        ),
      ),
      title: Text(pattern.descripcion),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.speed, size: 14, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Frecuencia: ${(pattern.frecuencia * 100).toInt()}%',
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(width: 16),
              Icon(Icons.assessment, size: 14, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Impacto: ${_getImpactText(pattern.impacto)}',
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
      trailing: IconButton(
        icon: const Icon(Icons.info_outline),
        onPressed: () => _showPatternDetails(pattern),
      ),
    );
  }

  Widget _buildRecommendationsSection(PatternAnalysisService service) {
    final patterns = service.detectedPatterns;
    final recommendations = _generateRecommendations(patterns);
    
    if (recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  '💡 Recomendaciones IA',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...recommendations.map((rec) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.arrow_right,
                    color: Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(rec),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  IconData _getPatternIcon(String tipo) {
    switch (tipo) {
      case 'temporal':
        return Icons.schedule;
      case 'verificador':
        return Icons.person;
      case 'sku':
        return Icons.inventory;
      case 'vh':
        return Icons.local_shipping;
      default:
        return Icons.analytics;
    }
  }

  String _getPatternTitle(String tipo) {
    switch (tipo) {
      case 'temporal':
        return 'Patrones Temporales';
      case 'verificador':
        return 'Patrones de Verificadores';
      case 'sku':
        return 'Patrones de SKUs';
      case 'vh':
        return 'Patrones de VH/Rutas';
      default:
        return 'Otros Patrones';
    }
  }

  Color _getImpactColor(double impacto) {
    if (impacto > 0.7) return Colors.red;
    if (impacto > 0.4) return Colors.orange;
    return Colors.green;
  }

  String _getImpactText(double impacto) {
    if (impacto > 0.7) return 'Alto';
    if (impacto > 0.4) return 'Medio';
    return 'Bajo';
  }

  List<String> _generateRecommendations(List<DetectedPattern> patterns) {
    final recommendations = <String>[];
    
    // Recomendaciones basadas en patrones detectados
    for (final pattern in patterns) {
      if (pattern.detalles.containsKey('recomendacion')) {
        recommendations.add(pattern.detalles['recomendacion']);
      }
    }
    
    // Recomendaciones generales
    if (patterns.any((p) => p.tipo == 'temporal')) {
      recommendations.add('Considerar ajustar horarios de trabajo basado en patrones temporales');
    }
    
    if (patterns.any((p) => p.tipo == 'verificador' && p.id.contains('problema'))) {
      recommendations.add('Programar entrenamiento adicional para verificadores con alta incidencia de novedades');
    }
    
    return recommendations.take(5).toList(); // Máximo 5 recomendaciones
  }

  String _formatDateTime(String isoString) {
    final dateTime = DateTime.parse(isoString);
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showPatternDetails(DetectedPattern pattern) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Detalles del Patrón'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Descripción: ${pattern.descripcion}'),
            const SizedBox(height: 8),
            Text('Tipo: ${pattern.tipo}'),
            const SizedBox(height: 8),
            Text('Frecuencia: ${(pattern.frecuencia * 100).toInt()}%'),
            const SizedBox(height: 8),
            Text('Impacto: ${_getImpactText(pattern.impacto)}'),
            const SizedBox(height: 8),
            Text('Detectado: ${_formatDateTime(pattern.fechaDeteccion.toIso8601String())}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshAnalysis() async {
    setState(() => _isLoading = true);
    
    final patternService = context.read<PatternAnalysisService>();
    await patternService.forceAnalysis();
    
    setState(() => _isLoading = false);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Análisis actualizado correctamente'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
