import 'dart:math';
import 'package:equatable/equatable.dart';

/// Modelos de Machine Learning para análisis avanzado - FASE 2

/// Predicción avanzada con múltiples algoritmos
class AdvancedPrediction extends Equatable {
  final String vhId;
  final String verificadorId;
  final DateTime fechaPrediccion;
  final double probabilidadNovedad;
  final String tipoNovedadMasProbable;
  final Map<String, double> probabilidadesTipo;
  final double confianza;
  final List<String> recomendaciones;
  final Map<String, dynamic> factoresInfluencia;
  final DemandaPrediction demandaPrediccion;
  final double riesgoOperacional;
  final List<ActionItem> accionesRecomendadas;

  const AdvancedPrediction({
    required this.vhId,
    required this.verificadorId,
    required this.fechaPrediccion,
    required this.probabilidadNovedad,
    required this.tipoNovedadMasProbable,
    required this.probabilidadesTipo,
    required this.confianza,
    required this.recomendaciones,
    required this.factoresInfluencia,
    required this.demandaPrediccion,
    required this.riesgoOperacional,
    required this.accionesRecomendadas,
  });

  factory AdvancedPrediction.defaultPrediction(String vhId, String verificadorId) {
    return AdvancedPrediction(
      vhId: vhId,
      verificadorId: verificadorId,
      fechaPrediccion: DateTime.now(),
      probabilidadNovedad: 0.1,
      tipoNovedadMasProbable: 'Faltante',
      probabilidadesTipo: const {'Faltante': 0.6, 'Sobrante': 0.4},
      confianza: 0.1,
      recomendaciones: const ['Análisis no disponible'],
      factoresInfluencia: const {},
      demandaPrediccion: DemandaPrediction.empty(),
      riesgoOperacional: 0.1,
      accionesRecomendadas: const [],
    );
  }

  @override
  List<Object?> get props => [
        vhId,
        verificadorId,
        fechaPrediccion,
        probabilidadNovedad,
        tipoNovedadMasProbable,
        probabilidadesTipo,
        confianza,
        recomendaciones,
        factoresInfluencia,
        demandaPrediccion,
        riesgoOperacional,
        accionesRecomendadas,
      ];
}

/// Predicción de demanda
class DemandaPrediction extends Equatable {
  final double demandaEsperada;
  final double variabilidad;
  final Map<String, double> factoresEstacionales;
  final List<String> alertas;

  const DemandaPrediction({
    required this.demandaEsperada,
    required this.variabilidad,
    required this.factoresEstacionales,
    required this.alertas,
  });

  factory DemandaPrediction.empty() {
    return const DemandaPrediction(
      demandaEsperada: 0.0,
      variabilidad: 0.0,
      factoresEstacionales: {},
      alertas: [],
    );
  }

  @override
  List<Object?> get props => [demandaEsperada, variabilidad, factoresEstacionales, alertas];
}

/// Item de acción recomendada
class ActionItem extends Equatable {
  final String id;
  final String titulo;
  final String descripcion;
  final String prioridad; // 'alta', 'media', 'baja'
  final String categoria; // 'verificador', 'proceso', 'sku', 'ruta'
  final DateTime fechaLimite;
  final Map<String, dynamic> metadatos;

  const ActionItem({
    required this.id,
    required this.titulo,
    required this.descripcion,
    required this.prioridad,
    required this.categoria,
    required this.fechaLimite,
    required this.metadatos,
  });

  @override
  List<Object?> get props => [id, titulo, descripcion, prioridad, categoria, fechaLimite, metadatos];
}

/// Métricas del modelo ML
class ModelMetrics extends Equatable {
  final double accuracy;
  final double precision;
  final double recall;
  final double f1Score;
  final int totalPredictions;
  final int correctPredictions;
  final DateTime lastEvaluation;
  final Map<String, double> classMetrics;

  const ModelMetrics({
    required this.accuracy,
    required this.precision,
    required this.recall,
    required this.f1Score,
    required this.totalPredictions,
    required this.correctPredictions,
    required this.lastEvaluation,
    required this.classMetrics,
  });

  factory ModelMetrics.empty() {
    return ModelMetrics(
      accuracy: 0.0,
      precision: 0.0,
      recall: 0.0,
      f1Score: 0.0,
      totalPredictions: 0,
      correctPredictions: 0,
      lastEvaluation: DateTime.now(),
      classMetrics: const {},
    );
  }

  @override
  List<Object?> get props => [
        accuracy,
        precision,
        recall,
        f1Score,
        totalPredictions,
        correctPredictions,
        lastEvaluation,
        classMetrics,
      ];
}

/// Modelo Random Forest simplificado
class RandomForestModel {
  final int numTrees;
  final int maxDepth;
  final int minSamplesLeaf;
  
  final List<DecisionTree> _trees = [];
  bool _isTrained = false;

  RandomForestModel({
    required this.numTrees,
    required this.maxDepth,
    required this.minSamplesLeaf,
  });

  Future<void> train(List<List<double>> features, List<double> labels) async {
    _trees.clear();
    final random = Random();
    
    for (int i = 0; i < numTrees; i++) {
      // Bootstrap sampling
      final bootstrapIndices = List.generate(
        features.length,
        (_) => random.nextInt(features.length),
      );
      
      final bootstrapFeatures = bootstrapIndices.map((i) => features[i]).toList();
      final bootstrapLabels = bootstrapIndices.map((i) => labels[i]).toList();
      
      // Entrenar árbol individual
      final tree = DecisionTree(maxDepth: maxDepth, minSamplesLeaf: minSamplesLeaf);
      await tree.train(bootstrapFeatures, bootstrapLabels);
      _trees.add(tree);
    }
    
    _isTrained = true;
  }

  double predict(List<double> features) {
    if (!_isTrained || _trees.isEmpty) return 0.1;
    
    // Promedio de predicciones de todos los árboles
    double sum = 0.0;
    for (final tree in _trees) {
      sum += tree.predict(features);
    }
    
    return sum / _trees.length;
  }
}

/// Árbol de decisión simplificado
class DecisionTree {
  final int maxDepth;
  final int minSamplesLeaf;
  
  TreeNode? _root;

  DecisionTree({required this.maxDepth, required this.minSamplesLeaf});

  Future<void> train(List<List<double>> features, List<double> labels) async {
    _root = _buildTree(features, labels, 0);
  }

  double predict(List<double> features) {
    return _root?.predict(features) ?? 0.1;
  }

  TreeNode _buildTree(List<List<double>> features, List<double> labels, int depth) {
    // Condiciones de parada
    if (depth >= maxDepth || labels.length <= minSamplesLeaf || _isPure(labels)) {
      return TreeNode.leaf(_calculateMean(labels));
    }

    // Encontrar mejor split
    final bestSplit = _findBestSplit(features, labels);
    if (bestSplit == null) {
      return TreeNode.leaf(_calculateMean(labels));
    }

    // Dividir datos
    final leftIndices = <int>[];
    final rightIndices = <int>[];
    
    for (int i = 0; i < features.length; i++) {
      if (features[i][bestSplit.featureIndex] <= bestSplit.threshold) {
        leftIndices.add(i);
      } else {
        rightIndices.add(i);
      }
    }

    // Recursión
    final leftFeatures = leftIndices.map((i) => features[i]).toList();
    final leftLabels = leftIndices.map((i) => labels[i]).toList();
    final rightFeatures = rightIndices.map((i) => features[i]).toList();
    final rightLabels = rightIndices.map((i) => labels[i]).toList();

    final leftChild = _buildTree(leftFeatures, leftLabels, depth + 1);
    final rightChild = _buildTree(rightFeatures, rightLabels, depth + 1);

    return TreeNode.internal(
      featureIndex: bestSplit.featureIndex,
      threshold: bestSplit.threshold,
      leftChild: leftChild,
      rightChild: rightChild,
    );
  }

  Split? _findBestSplit(List<List<double>> features, List<double> labels) {
    if (features.isEmpty) return null;
    
    double bestGain = -1;
    Split? bestSplit;
    
    final numFeatures = features[0].length;
    
    for (int featureIndex = 0; featureIndex < numFeatures; featureIndex++) {
      final values = features.map((f) => f[featureIndex]).toSet().toList()..sort();
      
      for (int i = 0; i < values.length - 1; i++) {
        final threshold = (values[i] + values[i + 1]) / 2;
        final gain = _calculateInformationGain(features, labels, featureIndex, threshold);
        
        if (gain > bestGain) {
          bestGain = gain;
          bestSplit = Split(featureIndex: featureIndex, threshold: threshold);
        }
      }
    }
    
    return bestSplit;
  }

  double _calculateInformationGain(List<List<double>> features, List<double> labels, int featureIndex, double threshold) {
    final leftLabels = <double>[];
    final rightLabels = <double>[];
    
    for (int i = 0; i < features.length; i++) {
      if (features[i][featureIndex] <= threshold) {
        leftLabels.add(labels[i]);
      } else {
        rightLabels.add(labels[i]);
      }
    }
    
    if (leftLabels.isEmpty || rightLabels.isEmpty) return 0;
    
    final totalEntropy = _calculateEntropy(labels);
    final leftWeight = leftLabels.length / labels.length;
    final rightWeight = rightLabels.length / labels.length;
    
    return totalEntropy - (leftWeight * _calculateEntropy(leftLabels) + rightWeight * _calculateEntropy(rightLabels));
  }

  double _calculateEntropy(List<double> labels) {
    if (labels.isEmpty) return 0;
    
    final counts = <double, int>{};
    for (final label in labels) {
      counts[label] = (counts[label] ?? 0) + 1;
    }
    
    double entropy = 0;
    for (final count in counts.values) {
      final p = count / labels.length;
      if (p > 0) {
        entropy -= p * log(p) / ln2;
      }
    }
    
    return entropy;
  }

  bool _isPure(List<double> labels) {
    return labels.toSet().length <= 1;
  }

  double _calculateMean(List<double> labels) {
    return labels.isEmpty ? 0 : labels.reduce((a, b) => a + b) / labels.length;
  }
}

/// Nodo del árbol de decisión
class TreeNode {
  final bool isLeaf;
  final double? value;
  final int? featureIndex;
  final double? threshold;
  final TreeNode? leftChild;
  final TreeNode? rightChild;

  TreeNode.leaf(this.value)
      : isLeaf = true,
        featureIndex = null,
        threshold = null,
        leftChild = null,
        rightChild = null;

  TreeNode.internal({
    required this.featureIndex,
    required this.threshold,
    required this.leftChild,
    required this.rightChild,
  })  : isLeaf = false,
        value = null;

  double predict(List<double> features) {
    if (isLeaf) {
      return value ?? 0.1;
    }
    
    if (features[featureIndex!] <= threshold!) {
      return leftChild!.predict(features);
    } else {
      return rightChild!.predict(features);
    }
  }
}

/// Split para árbol de decisión
class Split {
  final int featureIndex;
  final double threshold;

  Split({required this.featureIndex, required this.threshold});
}

/// Red neuronal simplificada
class NeuralNetworkModel {
  final int inputSize;
  final List<int> hiddenLayers;
  final int outputSize;
  final double learningRate;
  
  final List<List<List<double>>> _weights = [];
  final List<List<double>> _biases = [];
  bool _isTrained = false;

  NeuralNetworkModel({
    required this.inputSize,
    required this.hiddenLayers,
    required this.outputSize,
    required this.learningRate,
  });

  Future<void> train(List<List<double>> features, List<double> labels) async {
    _initializeWeights();
    
    // Entrenamiento simplificado (en producción usaríamos backpropagation completo)
    for (int epoch = 0; epoch < 100; epoch++) {
      for (int i = 0; i < features.length; i++) {
        final prediction = _forward(features[i]);
        final target = labels[i];
        _updateWeights(features[i], prediction, target);
      }
    }
    
    _isTrained = true;
  }

  Map<String, double> predictMulticlass(List<double> features) {
    if (!_isTrained) {
      return {'Faltante': 0.6, 'Sobrante': 0.4};
    }
    
    final output = _forward(features);
    final softmax = _applySoftmax(output);
    
    return {
      'Faltante': softmax[0],
      'Sobrante': softmax.length > 1 ? softmax[1] : 1 - softmax[0],
    };
  }

  void _initializeWeights() {
    final random = Random();
    _weights.clear();
    _biases.clear();
    
    final layers = [inputSize] + hiddenLayers + [outputSize];
    
    for (int i = 0; i < layers.length - 1; i++) {
      final layerWeights = List.generate(
        layers[i + 1],
        (_) => List.generate(layers[i], (_) => random.nextGaussian() * 0.1),
      );
      _weights.add(layerWeights);
      
      final layerBiases = List.generate(layers[i + 1], (_) => 0.0);
      _biases.add(layerBiases);
    }
  }

  List<double> _forward(List<double> input) {
    List<double> activation = List.from(input);
    
    for (int layer = 0; layer < _weights.length; layer++) {
      final newActivation = <double>[];
      
      for (int neuron = 0; neuron < _weights[layer].length; neuron++) {
        double sum = _biases[layer][neuron];
        for (int i = 0; i < activation.length; i++) {
          sum += activation[i] * _weights[layer][neuron][i];
        }
        newActivation.add(_sigmoid(sum));
      }
      
      activation = newActivation;
    }
    
    return activation;
  }

  void _updateWeights(List<double> input, List<double> prediction, double target) {
    // Implementación simplificada de actualización de pesos
    // En producción se usaría backpropagation completo
    final error = target - prediction[0];
    final learningStep = learningRate * error;
    
    // Actualizar solo la última capa (simplificado)
    if (_weights.isNotEmpty) {
      final lastLayer = _weights.length - 1;
      for (int i = 0; i < _weights[lastLayer][0].length; i++) {
        _weights[lastLayer][0][i] += learningStep * input[min(i, input.length - 1)];
      }
    }
  }

  List<double> _applySoftmax(List<double> values) {
    final maxVal = values.reduce(max);
    final expValues = values.map((v) => exp(v - maxVal)).toList();
    final sumExp = expValues.reduce((a, b) => a + b);
    return expValues.map((v) => v / sumExp).toList();
  }

  double _sigmoid(double x) {
    return 1.0 / (1.0 + exp(-x));
  }
}

/// Modelo de series temporales
class TimeSeriesModel {
  Map<DateTime, double> _historicalData = {};
  final List<double> _trend = [];
  final List<double> _seasonal = [];
  bool _isTrained = false;

  Future<void> train(Map<DateTime, int> dailyData) async {
    _historicalData = dailyData.map((k, v) => MapEntry(k, v.toDouble()));
    
    // Análisis de tendencia simple (promedio móvil)
    _calculateTrend();
    
    // Análisis estacional simple
    _calculateSeasonality();
    
    _isTrained = true;
  }

  DemandaPrediction predict(DateTime targetDate, String? ruta) {
    if (!_isTrained) {
      return DemandaPrediction.empty();
    }
    
    // Predicción simple basada en tendencia y estacionalidad
    final dayOfWeek = targetDate.weekday;
    final seasonalFactor = _getSeasonalFactor(dayOfWeek);
    final trendValue = _getTrendValue();
    
    final demandaEsperada = trendValue * seasonalFactor;
    final variabilidad = _calculateVariability();
    
    return DemandaPrediction(
      demandaEsperada: demandaEsperada,
      variabilidad: variabilidad,
      factoresEstacionales: {
        'dia_semana': seasonalFactor,
        'tendencia': trendValue,
      },
      alertas: _generateAlerts(demandaEsperada, variabilidad),
    );
  }

  void _calculateTrend() {
    final values = _historicalData.values.toList();
    if (values.length < 7) return;
    
    // Promedio móvil de 7 días
    for (int i = 6; i < values.length; i++) {
      final avg = values.sublist(i - 6, i + 1).reduce((a, b) => a + b) / 7;
      _trend.add(avg);
    }
  }

  void _calculateSeasonality() {
    final dayOfWeekData = <int, List<double>>{};
    
    for (final entry in _historicalData.entries) {
      final dayOfWeek = entry.key.weekday;
      dayOfWeekData.putIfAbsent(dayOfWeek, () => []).add(entry.value);
    }
    
    // Calcular promedio por día de la semana
    for (int day = 1; day <= 7; day++) {
      final dayData = dayOfWeekData[day] ?? [];
      if (dayData.isNotEmpty) {
        final avg = dayData.reduce((a, b) => a + b) / dayData.length;
        _seasonal.add(avg);
      } else {
        _seasonal.add(1.0);
      }
    }
  }

  double _getSeasonalFactor(int dayOfWeek) {
    if (_seasonal.isEmpty || dayOfWeek < 1 || dayOfWeek > 7) return 1.0;
    return _seasonal[dayOfWeek - 1];
  }

  double _getTrendValue() {
    return _trend.isEmpty ? 1.0 : _trend.last;
  }

  double _calculateVariability() {
    final values = _historicalData.values.toList();
    if (values.length < 2) return 0.1;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((v) => pow(v - mean, 2)).reduce((a, b) => a + b) / values.length;
    return sqrt(variance);
  }

  List<String> _generateAlerts(double demanda, double variabilidad) {
    final alerts = <String>[];
    
    if (demanda > 50) {
      alerts.add('Alta demanda esperada');
    }
    
    if (variabilidad > 10) {
      alerts.add('Alta variabilidad en la demanda');
    }
    
    return alerts;
  }
}

/// Extensión para generar números aleatorios gaussianos
extension RandomGaussian on Random {
  double nextGaussian() {
    double u1 = nextDouble();
    double u2 = nextDouble();
    return sqrt(-2 * log(u1)) * cos(2 * pi * u2);
  }
}
