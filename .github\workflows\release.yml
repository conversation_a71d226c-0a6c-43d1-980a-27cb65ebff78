name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release'
        required: true
        default: '1.0.0'

jobs:
  build:
    name: Build and Release
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run tests
      run: flutter test
      
    - name: Build Android APK
      run: flutter build apk --release --split-per-abi
      
    - name: Build Android AAB
      run: flutter build appbundle --release
      
    - name: Build Web
      run: flutter build web --release
      
    - name: Create Web ZIP
      run: |
        cd build/web
        zip -r ../../recount-pro-web.zip .
        cd ../..
        
    - name: Get version
      id: version
      run: |
        if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
          echo "VERSION=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
        else
          echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
        fi
        
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.version.outputs.VERSION }}
        release_name: ReCount Pro v${{ steps.version.outputs.VERSION }}
        body: |
          # ReCount Pro v${{ steps.version.outputs.VERSION }}
          
          ## 📱 Descargas
          
          ### Android
          - **ARM64 (Recomendado)**: Descarga `recount-pro-arm64.apk`
          - **ARM32**: Descarga `recount-pro-arm32.apk`
          - **x64**: Descarga `recount-pro-x64.apk`
          
          ### Web
          - **Aplicación Web**: Descarga `recount-pro-web.zip`
          
          ## 📋 Novedades
          - Sistema de actualizaciones automáticas
          - Mejoras en la interfaz de usuario
          - Corrección de errores y optimizaciones
          
          ## 📥 Instalación Android
          1. Descarga el APK correspondiente a tu dispositivo
          2. Habilita "Fuentes desconocidas" en Configuración > Seguridad
          3. Instala el APK descargado
          
          ## 🌐 Instalación Web
          1. Descarga el archivo ZIP
          2. Extrae en tu servidor web
          3. Accede a `index.html`
        draft: false
        prerelease: false
        
    - name: Upload ARM64 APK
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
        asset_name: recount-pro-arm64.apk
        asset_content_type: application/vnd.android.package-archive
        
    - name: Upload ARM32 APK
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk
        asset_name: recount-pro-arm32.apk
        asset_content_type: application/vnd.android.package-archive
        
    - name: Upload x64 APK
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: build/app/outputs/flutter-apk/app-x86_64-release.apk
        asset_name: recount-pro-x64.apk
        asset_content_type: application/vnd.android.package-archive
        
    - name: Upload AAB
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: build/app/outputs/bundle/release/app-release.aab
        asset_name: recount-pro.aab
        asset_content_type: application/octet-stream
        
    - name: Upload Web ZIP
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: recount-pro-web.zip
        asset_name: recount-pro-web.zip
        asset_content_type: application/zip
