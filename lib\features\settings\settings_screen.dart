import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/services/theme_service.dart';
import '../../core/services/localization_service.dart';
import '../../core/theme/app_theme.dart';

/// Pantalla de configuración de la aplicación
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.white),
            SizedBox(width: 8),
            Text('Configuración'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Apariencia'),
            _buildThemeSettings(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Idioma'),
            _buildLanguageSettings(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Notificaciones'),
            _buildNotificationSettings(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Datos'),
            _buildDataSettings(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Acerca de'),
            _buildAboutSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildThemeSettings() {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Card(
          elevation: 2,
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.palette, color: AppTheme.primaryColor),
                title: const Text('Tema'),
                subtitle: Text(_getThemeName(themeService.currentTheme)),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showThemeDialog(),
              ),
              const Divider(height: 1),
              SwitchListTile(
                secondary: const Icon(Icons.dark_mode, color: AppTheme.primaryColor),
                title: const Text('Modo Oscuro'),
                subtitle: const Text('Cambiar entre tema claro y oscuro'),
                value: themeService.isDarkMode,
                onChanged: (value) {
                  themeService.toggleDarkMode();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageSettings() {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        return Card(
          elevation: 2,
          child: ListTile(
            leading: const Icon(Icons.language, color: AppTheme.primaryColor),
            title: const Text('Idioma'),
            subtitle: Text(_getLanguageName(localizationService.currentLocale)),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showLanguageDialog(),
          ),
        );
      },
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          SwitchListTile(
            secondary: const Icon(Icons.notifications, color: AppTheme.primaryColor),
            title: const Text('Notificaciones Push'),
            subtitle: const Text('Recibir notificaciones de la aplicación'),
            value: true, // TODO: Implementar estado real
            onChanged: (value) {
              // TODO: Implementar lógica de notificaciones
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Función en desarrollo')),
              );
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.vibration, color: AppTheme.primaryColor),
            title: const Text('Vibración'),
            subtitle: const Text('Vibrar en notificaciones importantes'),
            value: true, // TODO: Implementar estado real
            onChanged: (value) {
              // TODO: Implementar lógica de vibración
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Función en desarrollo')),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDataSettings() {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.sync, color: AppTheme.primaryColor),
            title: const Text('Sincronización'),
            subtitle: const Text('Sincronizar datos con el servidor'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showSyncDialog();
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.storage, color: AppTheme.primaryColor),
            title: const Text('Almacenamiento'),
            subtitle: const Text('Gestionar datos locales'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showStorageDialog();
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.backup, color: AppTheme.primaryColor),
            title: const Text('Respaldo'),
            subtitle: const Text('Crear respaldo de datos'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showBackupDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSettings() {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.info, color: AppTheme.primaryColor),
            title: const Text('Versión'),
            subtitle: const Text('ReCount Pro v2.0.0'),
            onTap: () {
              _showAboutDialog();
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.help, color: AppTheme.primaryColor),
            title: const Text('Ayuda'),
            subtitle: const Text('Guía de usuario y soporte'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showHelpDialog();
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.privacy_tip, color: AppTheme.primaryColor),
            title: const Text('Privacidad'),
            subtitle: const Text('Política de privacidad'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showPrivacyDialog();
            },
          ),
        ],
      ),
    );
  }

  String _getThemeName(String theme) {
    switch (theme) {
      case 'default':
        return 'Predeterminado (Índigo-Teal)';
      case 'blue':
        return 'Azul';
      case 'green':
        return 'Verde';
      case 'purple':
        return 'Morado';
      default:
        return 'Predeterminado';
    }
  }

  String _getLanguageName(String locale) {
    switch (locale) {
      case 'es':
        return 'Español';
      case 'en':
        return 'English';
      default:
        return 'Español';
    }
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Seleccionar Tema'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildThemeOption('default', 'Predeterminado (Índigo-Teal)'),
            _buildThemeOption('blue', 'Azul'),
            _buildThemeOption('green', 'Verde'),
            _buildThemeOption('purple', 'Morado'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeOption(String themeKey, String themeName) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return RadioListTile<String>(
          title: Text(themeName),
          value: themeKey,
          groupValue: themeService.currentTheme,
          onChanged: (value) {
            if (value != null) {
              themeService.setTheme(value);
              Navigator.of(context).pop();
            }
          },
        );
      },
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Seleccionar Idioma'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption('es', 'Español'),
            _buildLanguageOption('en', 'English'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(String localeKey, String languageName) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        return RadioListTile<String>(
          title: Text(languageName),
          value: localeKey,
          groupValue: localizationService.currentLocale,
          onChanged: (value) {
            if (value != null) {
              localizationService.setLocale(value);
              Navigator.of(context).pop();
            }
          },
        );
      },
    );
  }

  void _showSyncDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sincronización'),
        content: const Text('¿Desea sincronizar los datos con el servidor?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sincronización iniciada')),
              );
            },
            child: const Text('Sincronizar'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Almacenamiento'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Información de almacenamiento:'),
            SizedBox(height: 8),
            Text('• Datos de usuario: 2.3 MB'),
            Text('• Cache de imágenes: 1.8 MB'),
            Text('• Datos de conteos: 5.2 MB'),
            Text('• Total: 9.3 MB'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cache limpiado')),
              );
            },
            child: const Text('Limpiar Cache'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Respaldo'),
        content: const Text('¿Desea crear un respaldo de sus datos?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Respaldo creado exitosamente')),
              );
            },
            child: const Text('Crear Respaldo'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'ReCount Pro',
      applicationVersion: '2.0.0',
      applicationIcon: const Icon(Icons.inventory_2, size: 48, color: AppTheme.primaryColor),
      children: const [
        Text('Sistema avanzado de conteo de inventarios con IA integrada.'),
        SizedBox(height: 8),
        Text('Desarrollado para optimizar procesos de verificación y conteo.'),
      ],
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ayuda'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Guía rápida:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('1. Inicie sesión con sus credenciales'),
              Text('2. Seleccione "Segundo Conteo" para comenzar'),
              Text('3. Ingrese la placa del VH a verificar'),
              Text('4. Complete el formulario de novedad si es necesario'),
              Text('5. Guarde el conteo al finalizar'),
              SizedBox(height: 16),
              Text('Para soporte técnico, contacte al administrador del sistema.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Política de Privacidad'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Recopilación de datos:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• Datos de usuario para autenticación'),
              Text('• Información de conteos para análisis'),
              Text('• Métricas de uso para mejoras'),
              SizedBox(height: 16),
              Text('Uso de datos:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• Optimización de procesos'),
              Text('• Análisis de patrones con IA'),
              Text('• Generación de reportes'),
              SizedBox(height: 16),
              Text('Sus datos están protegidos y no se comparten con terceros.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }
}
