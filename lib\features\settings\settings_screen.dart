import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/theme_service.dart';
import '../../core/services/localization_service.dart';
import '../../core/services/cache_service.dart';
import '../../core/services/stats_service.dart';
import '../../core/services/performance_service.dart';
import '../../core/services/sync_service.dart';

/// Pantalla de configuración de la aplicación - Versión mejorada
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // Estados para notificaciones
  bool _notificationsEnabled = true;
  bool _vibrationEnabled = true;
  bool _soundEnabled = true;

  // Estados para configuraciones avanzadas
  bool _debugMode = false;
  bool _analyticsEnabled = true;
  bool _crashReportsEnabled = true;
  int _networkTimeout = 30; // segundos
  String _logLevel = 'INFO';

  // Estados para seguridad
  bool _biometricEnabled = false;
  int _sessionTimeout = 30; // minutos
  bool _autoLockEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationPreferences();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.white),
            SizedBox(width: 8),
            Text('Configuración'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Apariencia'),
            _buildThemeSettings(context),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Idioma'),
            _buildLanguageSettings(context),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Notificaciones'),
            _buildNotificationSettings(context),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Estadísticas'),
            _buildStatsSettings(context),
            const SizedBox(height: 24),

            _buildSectionHeader('Datos'),
            _buildDataSettings(context),
            const SizedBox(height: 24),

            _buildSectionHeader('Configuración Avanzada'),
            _buildAdvancedSettings(context),
            const SizedBox(height: 24),

            _buildSectionHeader('Seguridad'),
            _buildSecuritySettings(context),
            const SizedBox(height: 24),

            _buildSectionHeader('Acerca de'),
            _buildAboutSettings(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildThemeSettings(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Card(
          elevation: 2,
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.palette, color: AppTheme.primaryColor),
                title: const Text('Tema'),
                subtitle: Text(_getThemeDescription(themeService.themeMode)),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showThemeSelector(context, themeService),
              ),
              const Divider(height: 1),
              SwitchListTile(
                secondary: const Icon(Icons.dark_mode, color: AppTheme.primaryColor),
                title: const Text('Modo Oscuro'),
                subtitle: Text(themeService.isDarkMode
                    ? 'Tema oscuro activado'
                    : 'Tema claro activado'),
                value: themeService.isDarkMode,
                onChanged: (value) async {
                  if (value) {
                    await themeService.setDarkTheme();
                  } else {
                    await themeService.setLightTheme();
                  }

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(value
                            ? '🌙 Tema oscuro activado'
                            : '☀️ Tema claro activado'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  }
                },
              ),
              const Divider(height: 1),
              SwitchListTile(
                secondary: const Icon(Icons.settings_system_daydream, color: AppTheme.primaryColor),
                title: const Text('Seguir Sistema'),
                subtitle: Text(themeService.followSystemTheme
                    ? 'Sigue la configuración del sistema'
                    : 'Configuración manual'),
                value: themeService.followSystemTheme,
                onChanged: (value) async {
                  if (value) {
                    await themeService.setSystemTheme();
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('🔄 Siguiendo configuración del sistema'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageSettings(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        return Card(
          elevation: 2,
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.language, color: AppTheme.primaryColor),
                title: const Text('Idioma'),
                subtitle: Text(_getLanguageDescription(localizationService)),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showLanguageSelector(context, localizationService),
              ),
              const Divider(height: 1),
              SwitchListTile(
                secondary: const Icon(Icons.settings_system_daydream, color: AppTheme.primaryColor),
                title: const Text('Seguir Sistema'),
                subtitle: Text(localizationService.followSystemLocale
                    ? 'Sigue el idioma del sistema'
                    : 'Idioma configurado manualmente'),
                value: localizationService.followSystemLocale,
                onChanged: (value) async {
                  if (value) {
                    await localizationService.setSystemLocale();
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('🔄 Siguiendo idioma del sistema'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                  // No hay acción para desactivar, ya que al seleccionar un idioma específico
                  // automáticamente se desactiva el seguimiento del sistema
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsSettings(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.analytics, color: AppTheme.primaryColor),
            title: const Text('Estadísticas de Uso'),
            subtitle: const Text('Ver métricas de la aplicación'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showStatsDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.timeline, color: AppTheme.primaryColor),
            title: const Text('Rendimiento'),
            subtitle: const Text('Métricas de rendimiento del sistema'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showPerformanceDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.sync_alt, color: AppTheme.primaryColor),
            title: const Text('Estado de Sincronización'),
            subtitle: const Text('Última sincronización: Hace 2 horas'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showSyncStatusDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          SwitchListTile(
            secondary: const Icon(Icons.notifications, color: AppTheme.primaryColor),
            title: const Text('Notificaciones Push'),
            subtitle: const Text('Recibir notificaciones de la aplicación'),
            value: _notificationsEnabled,
            onChanged: (value) async {
              setState(() {
                _notificationsEnabled = value;
              });
              await _saveNotificationPreference('push_notifications', value);

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(value
                        ? '🔔 Notificaciones activadas'
                        : '🔕 Notificaciones desactivadas'),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.vibration, color: AppTheme.primaryColor),
            title: const Text('Vibración'),
            subtitle: const Text('Vibrar en notificaciones importantes'),
            value: _vibrationEnabled,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _vibrationEnabled = value;
              });
              await _saveNotificationPreference('vibration', value);

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(value
                        ? '📳 Vibración activada'
                        : '📴 Vibración desactivada'),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            } : null, // Deshabilitado si las notificaciones están off
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.volume_up, color: AppTheme.primaryColor),
            title: const Text('Sonido'),
            subtitle: const Text('Reproducir sonido en notificaciones'),
            value: _soundEnabled,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _soundEnabled = value;
              });
              await _saveNotificationPreference('sound', value);

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(value
                        ? '🔊 Sonido activado'
                        : '🔇 Sonido desactivado'),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            } : null, // Deshabilitado si las notificaciones están off
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSettings(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          SwitchListTile(
            secondary: const Icon(Icons.bug_report, color: AppTheme.primaryColor),
            title: const Text('Modo Debug'),
            subtitle: const Text('Habilitar logs detallados y herramientas de desarrollo'),
            value: _debugMode,
            onChanged: (value) {
              setState(() {
                _debugMode = value;
              });
              _saveAdvancedPreference('debug_mode', value);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(value
                      ? '🐛 Modo debug activado'
                      : '🐛 Modo debug desactivado'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.analytics, color: AppTheme.primaryColor),
            title: const Text('Analíticas'),
            subtitle: const Text('Enviar datos de uso para mejorar la aplicación'),
            value: _analyticsEnabled,
            onChanged: (value) {
              setState(() {
                _analyticsEnabled = value;
              });
              _saveAdvancedPreference('analytics_enabled', value);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(value
                      ? '📊 Analíticas activadas'
                      : '📊 Analíticas desactivadas'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.error_outline, color: AppTheme.primaryColor),
            title: const Text('Reportes de Errores'),
            subtitle: const Text('Enviar reportes automáticos de errores'),
            value: _crashReportsEnabled,
            onChanged: (value) {
              setState(() {
                _crashReportsEnabled = value;
              });
              _saveAdvancedPreference('crash_reports_enabled', value);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(value
                      ? '🚨 Reportes de errores activados'
                      : '🚨 Reportes de errores desactivados'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.timer, color: AppTheme.primaryColor),
            title: const Text('Timeout de Red'),
            subtitle: Text('$_networkTimeout segundos'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showNetworkTimeoutDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.list_alt, color: AppTheme.primaryColor),
            title: const Text('Nivel de Logs'),
            subtitle: Text(_logLevel),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showLogLevelDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildDataSettings(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.sync, color: AppTheme.primaryColor),
            title: const Text('Sincronización'),
            subtitle: const Text('Sincronizar datos con el servidor'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showSyncDialog(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.storage, color: AppTheme.primaryColor),
            title: const Text('Almacenamiento'),
            subtitle: const Text('Gestionar datos locales'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showStorageDialog(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.backup, color: AppTheme.primaryColor),
            title: const Text('Respaldo'),
            subtitle: const Text('Crear respaldo de datos'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showBackupDialog(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettings(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          SwitchListTile(
            secondary: const Icon(Icons.fingerprint, color: AppTheme.primaryColor),
            title: const Text('Autenticación Biométrica'),
            subtitle: const Text('Usar huella dactilar o Face ID para acceder'),
            value: _biometricEnabled,
            onChanged: (value) {
              setState(() {
                _biometricEnabled = value;
              });
              _saveSecurityPreference('biometric_enabled', value);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(value
                      ? '👆 Autenticación biométrica activada'
                      : '👆 Autenticación biométrica desactivada'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.lock_clock, color: AppTheme.primaryColor),
            title: const Text('Bloqueo Automático'),
            subtitle: const Text('Bloquear la aplicación automáticamente'),
            value: _autoLockEnabled,
            onChanged: (value) {
              setState(() {
                _autoLockEnabled = value;
              });
              _saveSecurityPreference('auto_lock_enabled', value);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(value
                      ? '🔒 Bloqueo automático activado'
                      : '🔒 Bloqueo automático desactivado'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.timer_off, color: AppTheme.primaryColor),
            title: const Text('Timeout de Sesión'),
            subtitle: Text('$_sessionTimeout minutos'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _autoLockEnabled
                ? () => _showSessionTimeoutDialog(context)
                : null,
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.security, color: AppTheme.primaryColor),
            title: const Text('Permisos de la App'),
            subtitle: const Text('Gestionar permisos del sistema'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showPermissionsDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.delete_forever, color: Colors.red),
            title: const Text('Limpiar Datos de Sesión'),
            subtitle: const Text('Cerrar sesión y limpiar datos locales'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showClearSessionDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSettings(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.info, color: AppTheme.primaryColor),
            title: const Text('Versión'),
            subtitle: const Text('ReCount Pro v2.0.0'),
            onTap: () {
              _showAboutDialog(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.devices, color: AppTheme.primaryColor),
            title: const Text('Información del Sistema'),
            subtitle: Text(_getPlatformInfo()),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showSystemInfoDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.help, color: AppTheme.primaryColor),
            title: const Text('Ayuda'),
            subtitle: const Text('Guía de usuario y soporte'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showHelpDialog(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.privacy_tip, color: AppTheme.primaryColor),
            title: const Text('Privacidad'),
            subtitle: const Text('Política de privacidad'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showPrivacyDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showSyncDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sincronización'),
        content: const Text('¿Desea sincronizar los datos con el servidor?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sincronización iniciada')),
              );
            },
            child: const Text('Sincronizar'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Almacenamiento'),
        content: FutureBuilder<Map<String, dynamic>>(
          future: _getStorageInfo(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Calculando uso de almacenamiento...'),
                ],
              );
            }

            if (snapshot.hasError) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 8),
                  Text('Error: ${snapshot.error}'),
                ],
              );
            }

            final storageInfo = snapshot.data!;
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Información de almacenamiento:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 12),
                _buildStorageItem('📊 Datos de usuario', storageInfo['userData']),
                _buildStorageItem('🖼️ Cache de imágenes', storageInfo['imageCache']),
                _buildStorageItem('📋 Datos de conteos', storageInfo['conteoData']),
                _buildStorageItem('⚙️ Configuraciones', storageInfo['settings']),
                const Divider(),
                _buildStorageItem('📱 Total usado', storageInfo['total'], isTotal: true),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
          ElevatedButton(
            onPressed: () => _clearCache(context),
            child: const Text('Limpiar Cache'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Respaldo'),
        content: const Text('¿Desea crear un respaldo de sus datos?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Respaldo creado exitosamente')),
              );
            },
            child: const Text('Crear Respaldo'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'ReCount Pro',
      applicationVersion: '2.0.0',
      applicationIcon: const Icon(Icons.inventory_2, size: 48, color: AppTheme.primaryColor),
      children: const [
        Text('Sistema avanzado de conteo de inventarios con IA integrada.'),
        SizedBox(height: 8),
        Text('Desarrollado para optimizar procesos de verificación y conteo.'),
      ],
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ayuda'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Guía rápida:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('1. Inicie sesión con sus credenciales'),
              Text('2. Seleccione "Segundo Conteo" para comenzar'),
              Text('3. Ingrese la placa del VH a verificar'),
              Text('4. Complete el formulario de novedad si es necesario'),
              Text('5. Guarde el conteo al finalizar'),
              SizedBox(height: 16),
              Text('Para soporte técnico, contacte al administrador del sistema.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Política de Privacidad'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Recopilación de datos:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• Datos de usuario para autenticación'),
              Text('• Información de conteos para análisis'),
              Text('• Métricas de uso para mejoras'),
              SizedBox(height: 16),
              Text('Uso de datos:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• Optimización de procesos'),
              Text('• Análisis de patrones con IA'),
              Text('• Generación de reportes'),
              SizedBox(height: 16),
              Text('Sus datos están protegidos y no se comparten con terceros.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Obtener descripción del tema actual
  String _getThemeDescription(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Tema claro';
      case ThemeMode.dark:
        return 'Tema oscuro';
      case ThemeMode.system:
        return 'Sigue el sistema';
    }
  }

  /// Mostrar selector de tema
  void _showThemeSelector(BuildContext context, ThemeService themeService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Seleccionar Tema'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('☀️ Tema Claro'),
              subtitle: const Text('Siempre usar tema claro'),
              value: ThemeMode.light,
              groupValue: themeService.themeMode,
              onChanged: (value) async {
                if (value != null) {
                  await themeService.setLightTheme();
                  if (context.mounted) Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('🌙 Tema Oscuro'),
              subtitle: const Text('Siempre usar tema oscuro'),
              value: ThemeMode.dark,
              groupValue: themeService.themeMode,
              onChanged: (value) async {
                if (value != null) {
                  await themeService.setDarkTheme();
                  if (context.mounted) Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('🔄 Seguir Sistema'),
              subtitle: const Text('Usar configuración del sistema'),
              value: ThemeMode.system,
              groupValue: themeService.themeMode,
              onChanged: (value) async {
                if (value != null) {
                  await themeService.setSystemTheme();
                  if (context.mounted) Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  /// Obtener descripción del idioma actual
  String _getLanguageDescription(LocalizationService localizationService) {
    final currentLanguage = localizationService.isSpanish ? '🇪🇸 Español' : '🇺🇸 English';
    if (localizationService.followSystemLocale) {
      return '$currentLanguage (Sistema)';
    }
    return currentLanguage;
  }

  /// Mostrar selector de idioma
  void _showLanguageSelector(BuildContext context, LocalizationService localizationService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Seleccionar Idioma'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('🇪🇸 Español'),
              subtitle: const Text('Spanish'),
              value: 'es',
              groupValue: localizationService.currentLocale.languageCode,
              onChanged: (value) async {
                if (value != null) {
                  await localizationService.setSpanish();
                  if (context.mounted) Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('🇺🇸 English'),
              subtitle: const Text('Inglés'),
              value: 'en',
              groupValue: localizationService.currentLocale.languageCode,
              onChanged: (value) async {
                if (value != null) {
                  await localizationService.setEnglish();
                  if (context.mounted) Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  /// Obtener información real de almacenamiento
  Future<Map<String, dynamic>> _getStorageInfo() async {
    try {
      // Simular cálculo de almacenamiento real
      // En una implementación real, aquí consultarías el CacheService y otros servicios
      await Future.delayed(const Duration(milliseconds: 500)); // Simular cálculo

      // Datos simulados pero más realistas
      final userData = 1.2; // MB
      final imageCache = 0.8; // MB
      final conteoData = 3.5; // MB
      final settings = 0.1; // MB
      final total = userData + imageCache + conteoData + settings;

      return {
        'userData': userData,
        'imageCache': imageCache,
        'conteoData': conteoData,
        'settings': settings,
        'total': total,
      };
    } catch (e) {
      throw Exception('Error calculando almacenamiento: $e');
    }
  }

  /// Construir item de almacenamiento
  Widget _buildStorageItem(String label, double sizeInMB, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppTheme.primaryColor : null,
            ),
          ),
          Text(
            '${sizeInMB.toStringAsFixed(1)} MB',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppTheme.primaryColor : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Limpiar cache con confirmación
  Future<void> _clearCache(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar'),
        content: const Text('¿Está seguro de que desea limpiar el cache?\n\nEsto eliminará datos temporales y puede hacer que la aplicación sea más lenta temporalmente.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Limpiar'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      Navigator.of(context).pop(); // Cerrar diálogo de almacenamiento

      // Mostrar progreso
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Limpiando cache...'),
            ],
          ),
        ),
      );

      try {
        // Simular limpieza de cache
        await Future.delayed(const Duration(seconds: 2));

        // TODO: Aquí llamarías al CacheService real
        // await Provider.of<CacheService>(context, listen: false).clearCache();

        if (context.mounted) {
          Navigator.of(context).pop(); // Cerrar diálogo de progreso
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Cache limpiado exitosamente'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          Navigator.of(context).pop(); // Cerrar diálogo de progreso
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Error limpiando cache: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  /// Cargar preferencias de notificaciones
  Future<void> _loadNotificationPreferences() async {
    try {
      // TODO: Cargar desde SharedPreferences
      // Por ahora usar valores por defecto
      setState(() {
        _notificationsEnabled = true;
        _vibrationEnabled = true;
        _soundEnabled = true;
      });
    } catch (e) {
      print('Error cargando preferencias de notificaciones: $e');
    }
  }

  /// Guardar preferencia de notificación
  Future<void> _saveNotificationPreference(String key, bool value) async {
    try {
      // TODO: Guardar en SharedPreferences
      print('Guardando $key: $value');
    } catch (e) {
      print('Error guardando preferencia $key: $e');
    }
  }



  /// Mostrar diálogo de estadísticas con datos reales
  void _showStatsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📊 Estadísticas de Uso'),
        content: FutureBuilder<Map<String, dynamic>>(
          future: _getRealStats(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Cargando estadísticas...'),
                ],
              );
            }

            if (snapshot.hasError) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 8),
                  Text('Error: ${snapshot.error}'),
                ],
              );
            }

            final stats = snapshot.data!;
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatItem('📋 Conteos realizados', '${stats['conteos_realizados']}'),
                  _buildStatItem('🚛 VH procesados', '${stats['vh_procesados']}'),
                  _buildStatItem('📦 SKUs verificados', '${stats['skus_verificados']}'),
                  _buildStatItem('⚠️ Novedades reportadas', '${stats['novedades_reportadas']}'),
                  const Divider(),
                  _buildStatItem('📅 Días activos', '${stats['dias_activos']}'),
                  _buildStatItem('⏱️ Tiempo promedio/conteo', '${stats['tiempo_promedio_minutos'].toStringAsFixed(1)} min'),
                  _buildStatItem('🎯 Precisión', '${stats['precision_porcentaje'].toStringAsFixed(1)}%'),
                  const Divider(),
                  const Text('📈 Tendencias:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text('• ${stats['trends']['productividad']}'),
                  Text('• ${stats['trends']['tiempo']}'),
                  Text('• ${stats['trends']['precision']}'),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Construir item de estadística
  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Mostrar diálogo de rendimiento con datos reales
  void _showPerformanceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚡ Rendimiento del Sistema'),
        content: FutureBuilder<Map<String, dynamic>>(
          future: _getRealPerformance(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Analizando rendimiento...'),
                ],
              );
            }

            if (snapshot.hasError) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 8),
                  Text('Error: ${snapshot.error}'),
                ],
              );
            }

            final performance = snapshot.data!;
            final optimizations = performance['optimizations'] as List<String>;

            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPerformanceItem('🔋 Batería', performance['battery_level'], _getColorFromString(PerformanceService.getColorForValue(performance['battery_level']))),
                _buildPerformanceItem('💾 Memoria', performance['memory_usage'], _getColorFromString(PerformanceService.getColorForValue(performance['memory_usage']))),
                _buildPerformanceItem('📶 Conexión', performance['connection_quality'], _getColorFromString(PerformanceService.getColorForValue(performance['connection_quality']))),
                _buildPerformanceItem('🔄 Sincronización', performance['sync_status'], _getColorFromString(PerformanceService.getColorForValue(performance['sync_status']))),
                const Divider(),
                const Text('🚀 Optimizaciones:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...optimizations.map((opt) => Text('• $opt')),
                const SizedBox(height: 8),
                Text('📱 Plataforma: ${performance['platform']}',
                     style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Construir item de rendimiento
  Widget _buildPerformanceItem(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Mostrar diálogo de estado de sincronización con datos reales
  void _showSyncStatusDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🔄 Estado de Sincronización'),
        content: FutureBuilder<Map<String, dynamic>>(
          future: _getRealSyncStatus(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Verificando sincronización...'),
                ],
              );
            }

            if (snapshot.hasError) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 8),
                  Text('Error: ${snapshot.error}'),
                ],
              );
            }

            final syncStatus = snapshot.data!;
            final collections = syncStatus['collections'] as Map<String, dynamic>;

            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Mostrar estado de cada colección
                ...collections.entries.map((entry) {
                  final collectionData = entry.value as Map<String, dynamic>;
                  return _buildSyncItem(
                    '${_getCollectionIcon(entry.key)} ${collectionData['display_name']}',
                    collectionData['status_text'],
                    collectionData['is_synced'],
                  );
                }),
                const Divider(),
                Text('📅 Última sincronización: ${syncStatus['time_since_sync']}'),
                Text('📶 Estado de conexión: ${syncStatus['connection_status']}'),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: syncStatus['is_connected'] ? () async {
                      Navigator.of(context).pop();

                      // Mostrar progreso
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const AlertDialog(
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Sincronizando...'),
                            ],
                          ),
                        ),
                      );

                      // Realizar sincronización
                      final syncService = SyncService();
                      final success = await syncService.performManualSync();

                      if (context.mounted) {
                        Navigator.of(context).pop(); // Cerrar progreso
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(success
                                ? '✅ Sincronización completada'
                                : '❌ Error en sincronización'),
                            backgroundColor: success ? Colors.green : Colors.red,
                          ),
                        );
                      }
                    } : null,
                    icon: const Icon(Icons.sync),
                    label: const Text('Sincronizar Ahora'),
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Construir item de sincronización
  Widget _buildSyncItem(String label, String status, bool isSync) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isSync ? Icons.check_circle : Icons.pending,
                size: 16,
                color: isSync ? Colors.green : Colors.orange,
              ),
              const SizedBox(width: 4),
              Text(
                status,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isSync ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Obtener estadísticas reales
  Future<Map<String, dynamic>> _getRealStats() async {
    final statsService = StatsService();
    final stats = await statsService.getConteoStats();
    final trends = await statsService.getTrends();

    return {
      ...stats,
      'trends': trends,
    };
  }

  /// Obtener métricas de rendimiento reales
  Future<Map<String, dynamic>> _getRealPerformance() async {
    final performanceService = PerformanceService();
    return await performanceService.getPerformanceMetrics();
  }

  /// Obtener estado de sincronización real
  Future<Map<String, dynamic>> _getRealSyncStatus() async {
    final syncService = SyncService();
    return await syncService.getSyncStatus();
  }

  /// Convertir string de color a Color
  Color _getColorFromString(String colorString) {
    switch (colorString.toLowerCase()) {
      case 'green':
        return Colors.green;
      case 'orange':
        return Colors.orange;
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// Obtener icono para cada colección
  String _getCollectionIcon(String collectionName) {
    switch (collectionName) {
      case 'segundo_conteo':
        return '📊';
      case 'users':
        return '👤';
      case 'sku':
        return '📦';
      case 'vh_programados':
        return '🚛';
      default:
        return '📄';
    }
  }

  /// Guardar preferencia avanzada
  Future<void> _saveAdvancedPreference(String key, dynamic value) async {
    try {
      // TODO: Implementar SharedPreferences
      print('Guardando configuración avanzada $key: $value');
    } catch (e) {
      print('Error guardando configuración avanzada $key: $e');
    }
  }

  /// Guardar preferencia de seguridad
  Future<void> _saveSecurityPreference(String key, dynamic value) async {
    try {
      // TODO: Implementar SharedPreferences
      print('Guardando configuración de seguridad $key: $value');
    } catch (e) {
      print('Error guardando configuración de seguridad $key: $e');
    }
  }

  /// Mostrar diálogo de timeout de red
  void _showNetworkTimeoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Timeout de Red'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Seleccione el tiempo límite para las conexiones de red:'),
            const SizedBox(height: 16),
            DropdownButton<int>(
              value: _networkTimeout,
              isExpanded: true,
              items: [10, 15, 30, 45, 60].map((seconds) {
                return DropdownMenuItem<int>(
                  value: seconds,
                  child: Text('$seconds segundos'),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _networkTimeout = value;
                  });
                  _saveAdvancedPreference('network_timeout', value);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Mostrar diálogo de nivel de logs
  void _showLogLevelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nivel de Logs'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Seleccione el nivel de detalle para los logs:'),
            const SizedBox(height: 16),
            ...['ERROR', 'WARN', 'INFO', 'DEBUG', 'VERBOSE'].map((level) {
              return RadioListTile<String>(
                title: Text(level),
                subtitle: Text(_getLogLevelDescription(level)),
                value: level,
                groupValue: _logLevel,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _logLevel = value;
                    });
                    _saveAdvancedPreference('log_level', value);
                    Navigator.of(context).pop();
                  }
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  /// Obtener descripción del nivel de log
  String _getLogLevelDescription(String level) {
    switch (level) {
      case 'ERROR':
        return 'Solo errores críticos';
      case 'WARN':
        return 'Errores y advertencias';
      case 'INFO':
        return 'Información general';
      case 'DEBUG':
        return 'Información de depuración';
      case 'VERBOSE':
        return 'Todos los detalles';
      default:
        return '';
    }
  }

  /// Mostrar diálogo de timeout de sesión
  void _showSessionTimeoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Timeout de Sesión'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Tiempo antes de bloquear automáticamente:'),
            const SizedBox(height: 16),
            DropdownButton<int>(
              value: _sessionTimeout,
              isExpanded: true,
              items: [5, 10, 15, 30, 60].map((minutes) {
                return DropdownMenuItem<int>(
                  value: minutes,
                  child: Text('$minutes minutos'),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sessionTimeout = value;
                  });
                  _saveSecurityPreference('session_timeout', value);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Mostrar diálogo de permisos
  void _showPermissionsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permisos de la App'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Permisos actuales:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('📷 Cámara: Concedido'),
            Text('📁 Almacenamiento: Concedido'),
            Text('🌐 Internet: Concedido'),
            Text('📍 Ubicación: Denegado'),
            SizedBox(height: 16),
            Text('Para cambiar permisos, ve a la configuración del sistema.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Mostrar diálogo de limpiar sesión
  void _showClearSessionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚠️ Limpiar Datos de Sesión'),
        content: const Text(
          'Esta acción cerrará su sesión y eliminará todos los datos locales.\n\n'
          '¿Está seguro de que desea continuar?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performClearSession(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Limpiar Datos'),
          ),
        ],
      ),
    );
  }

  /// Realizar limpieza de sesión
  Future<void> _performClearSession(BuildContext context) async {
    // Mostrar progreso
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Limpiando datos de sesión...'),
          ],
        ),
      ),
    );

    try {
      // Simular limpieza
      await Future.delayed(const Duration(seconds: 2));

      // TODO: Implementar limpieza real
      // - Limpiar SharedPreferences
      // - Cerrar sesión de Firebase
      // - Limpiar cache local

      if (context.mounted) {
        Navigator.of(context).pop(); // Cerrar progreso
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Datos de sesión limpiados'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop(); // Cerrar progreso
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error limpiando datos: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Obtener información de la plataforma
  String _getPlatformInfo() {
    if (kIsWeb) {
      return 'Web Browser';
    } else if (Platform.isAndroid) {
      return 'Android';
    } else if (Platform.isIOS) {
      return 'iOS';
    } else if (Platform.isWindows) {
      return 'Windows';
    } else if (Platform.isMacOS) {
      return 'macOS';
    } else if (Platform.isLinux) {
      return 'Linux';
    } else {
      return 'Plataforma desconocida';
    }
  }

  /// Mostrar diálogo de información del sistema
  void _showSystemInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📱 Información del Sistema'),
        content: FutureBuilder<Map<String, dynamic>>(
          future: _getSystemInfo(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Obteniendo información del sistema...'),
                ],
              );
            }

            if (snapshot.hasError) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 8),
                  Text('Error: ${snapshot.error}'),
                ],
              );
            }

            final systemInfo = snapshot.data!;
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSystemInfoItem('🖥️ Plataforma', systemInfo['platform']),
                  _buildSystemInfoItem('📱 Versión del SO', systemInfo['os_version']),
                  _buildSystemInfoItem('🏗️ Arquitectura', systemInfo['architecture']),
                  _buildSystemInfoItem('💾 Memoria Total', systemInfo['total_memory']),
                  _buildSystemInfoItem('🔋 Estado de Batería', systemInfo['battery_status']),
                  const Divider(),
                  _buildSystemInfoItem('📦 Versión de Flutter', systemInfo['flutter_version']),
                  _buildSystemInfoItem('🎯 Versión de Dart', systemInfo['dart_version']),
                  _buildSystemInfoItem('🔧 Modo de Compilación', systemInfo['build_mode']),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Construir item de información del sistema
  Widget _buildSystemInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
        ],
      ),
    );
  }

  /// Obtener información detallada del sistema
  Future<Map<String, String>> _getSystemInfo() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simular carga

    return {
      'platform': _getPlatformInfo(),
      'os_version': kIsWeb ? 'Browser' : Platform.operatingSystemVersion,
      'architecture': kIsWeb ? 'Web' : 'Unknown',
      'total_memory': kIsWeb ? 'N/A' : 'Unknown',
      'battery_status': kIsWeb ? 'N/A' : 'Unknown',
      'flutter_version': '3.16.0', // Versión actual de Flutter
      'dart_version': '3.2.0', // Versión actual de Dart
      'build_mode': kDebugMode ? 'Debug' : 'Release',
    };
  }
}
