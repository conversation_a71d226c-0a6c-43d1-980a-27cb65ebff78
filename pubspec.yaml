name: recount_pro
description: App Flutter para segundo conteo diario de vehículos en centros de distribución
version: 1.0.1+2

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # Firebase - Solo las necesarias
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  
  # UI
  cupertino_icons: ^1.0.2
  
  # State Management
  provider: ^6.1.1
  
  # PDF Generation
  pdf: ^3.10.7
  printing: ^5.11.1
  
  # Excel Import
  excel: ^4.0.2
  
  # Utils
  path: ^1.8.3
  path_provider: ^2.1.1
  share_plus: ^7.2.1
  package_info_plus: ^4.2.0
  url_launcher: ^6.2.1

  # Storage
  shared_preferences: ^2.2.2

  # Network
  connectivity_plus: ^6.0.5

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # Data
  equatable: ^2.0.5
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

  # Testing
  mockito: ^5.4.4
  build_runner: ^2.4.7
  fake_cloud_firestore: ^3.0.2
  integration_test:
    sdk: flutter

  # Icon Generation
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/data/

# Configuración de iconos de la aplicación
flutter_launcher_icons:
  android: "launcher_icon"
  ios: false
  image_path: "assets/images/app_icon.png"
  min_sdk_android: 23
  web:
    generate: true
    image_path: "assets/images/app_icon.png"
    background_color: "#667eea"
    theme_color: "#764ba2"
  windows:
    generate: true
    image_path: "assets/images/app_icon.png"
    icon_size: 48
  remove_alpha_ios: true